"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { apiClient } from '@/lib/api-client'
import Header from "@/components/Header"
import Footer from "@/components/Footer"
import FloatingActionBar from "@/components/FloatingActionBar"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Loader2, ArrowLeft, Calendar, MapPin, Ruler, Eye } from 'lucide-react'
import Link from 'next/link'
import { motion } from "framer-motion"

interface Project {
  id: number
  title: string
  slug: string
  description: string
  content: string
  images: string[]
  location: string
  area: string
  year: number
  category_id: number | null
  featured: boolean
  status: 'draft' | 'published' | 'archived'
  created_at: string
  updated_at: string
}

export default function ProjectDetailPage() {
  const params = useParams()
  const projectId = parseInt(params.id as string)
  const [project, setProject] = useState<Project | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedImageIndex, setSelectedImageIndex] = useState(0)

  useEffect(() => {
    fetchProject()
  }, [projectId])

  const fetchProject = async () => {
    try {
      const response = await apiClient.getProject(projectId)
      if (response.success && response.data) {
        setProject(response.data)
      } else {
        setError('Dự án không tồn tại')
      }
    } catch (error) {
      console.error('Error fetching project:', error)
      setError('Không thể tải thông tin dự án')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="flex items-center justify-center h-[60vh] mt-[120px]">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-8 w-8 animate-spin text-[#3B82F6]" />
            <span className="text-lg">Đang tải dự án...</span>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  if (error || !project) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="flex items-center justify-center h-[60vh] mt-[120px]">
          <div className="text-center">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Không tìm thấy dự án</h2>
            <p className="text-gray-600 mb-6">{error || 'Dự án không tồn tại'}</p>
            <Link href="/du-an-da-hoan-thien">
              <Button className="bg-[#3B82F6] hover:bg-[#2563EB] text-white">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Quay lại danh sách dự án
              </Button>
            </Link>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  const projectImages = project.images && project.images.length > 0 ? project.images : [
    "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=800"
  ]

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Back Button */}
      <div className="mt-[120px] py-4 bg-gray-50">
        <div className="container mx-auto px-4">
          <Link href="/du-an-da-hoan-thien">
            <Button variant="ghost" className="text-[#3B82F6] hover:bg-[#3B82F6]/10">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Quay lại danh sách dự án
            </Button>
          </Link>
        </div>
      </div>

      {/* Project Header */}
      <section className="py-8 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-4xl md:text-5xl font-bold text-[#1F2937] mb-4">
              {project.title}
            </h1>
            <p className="text-xl text-[#6B7280] mb-6">
              {project.description}
            </p>
            
            {/* Project Info */}
            <div className="flex flex-wrap gap-6 text-sm">
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4 text-[#3B82F6]" />
                <span className="text-[#6B7280]">Địa điểm:</span>
                <span className="font-medium text-[#1F2937]">{project.location}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Ruler className="h-4 w-4 text-[#3B82F6]" />
                <span className="text-[#6B7280]">Diện tích:</span>
                <span className="font-medium text-[#1F2937]">{project.area}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-[#3B82F6]" />
                <span className="text-[#6B7280]">Năm hoàn thành:</span>
                <span className="font-medium text-[#1F2937]">{project.year}</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Image Gallery */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {/* Main Image */}
            <div className="relative h-[500px] md:h-[600px] rounded-lg overflow-hidden mb-6">
              <Image
                src={projectImages[selectedImageIndex]}
                alt={project.title}
                fill
                className="object-cover"
                priority
              />
            </div>

            {/* Thumbnail Gallery */}
            {projectImages.length > 1 && (
              <div className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-4">
                {projectImages.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`relative h-20 rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                      selectedImageIndex === index 
                        ? 'border-[#3B82F6] ring-2 ring-[#3B82F6]/20' 
                        : 'border-gray-200 hover:border-[#3B82F6]/50'
                    }`}
                  >
                    <Image
                      src={image}
                      alt={`${project.title} - Hình ${index + 1}`}
                      fill
                      className="object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </motion.div>
        </div>
      </section>

      {/* Project Content */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="max-w-4xl mx-auto"
          >
            <h2 className="text-3xl font-bold text-[#1F2937] mb-6">Chi Tiết Dự Án</h2>
            <div className="prose prose-lg max-w-none">
              {project.content ? (
                <div 
                  className="text-[#374151] leading-relaxed"
                  dangerouslySetInnerHTML={{ __html: project.content }}
                />
              ) : (
                <p className="text-[#374151] leading-relaxed">
                  Đây là một dự án thiết kế nội thất đẳng cấp được thực hiện bởi đội ngũ kiến trúc sư 
                  chuyên nghiệp. Chúng tôi đã tập trung vào việc tối ưu hóa không gian sống, 
                  tạo ra một môi trường sống hiện đại và tiện nghi cho gia chủ.
                </p>
              )}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className="py-16 bg-[#3B82F6]">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Bạn có dự án tương tự?
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Hãy để chúng tôi giúp bạn biến ý tưởng thành hiện thực với dịch vụ thiết kế nội thất chuyên nghiệp
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/gui-yeu-cau">
                <Button size="lg" className="bg-white text-[#3B82F6] hover:bg-gray-100">
                  Gửi Yêu Cầu Tư Vấn
                </Button>
              </Link>
              <Link href="/du-an-da-hoan-thien">
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-[#3B82F6]">
                  <Eye className="mr-2 h-4 w-4" />
                  Xem Thêm Dự Án
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
      <FloatingActionBar />
    </div>
  )
}