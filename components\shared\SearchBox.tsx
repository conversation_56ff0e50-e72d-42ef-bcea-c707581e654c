import { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Search, X } from 'lucide-react'

interface SearchBoxProps {
  placeholder?: string
  value?: string
  onSearch: (query: string) => void
  onClear?: () => void
  className?: string
  showClearButton?: boolean
}

export default function SearchBox({ 
  placeholder = "Tìm kiếm...",
  value = "",
  onSearch,
  onClear,
  className = "",
  showClearButton = true
}: SearchBoxProps) {
  const [searchQuery, setSearchQuery] = useState(value)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch(searchQuery.trim())
  }

  const handleClear = () => {
    setSearchQuery('')
    onSearch('')
    onClear?.()
  }

  return (
    <form onSubmit={handleSubmit} className={`relative ${className}`}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          type="text"
          placeholder={placeholder}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 pr-20"
        />
        
        {showClearButton && searchQuery && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            <X className="h-4 w-4" />
          </button>
        )}
        
        <Button
          type="submit"
          size="sm"
          className="absolute right-1 top-1/2 transform -translate-y-1/2"
        >
          <Search className="h-4 w-4" />
        </Button>
      </div>
    </form>
  )
}
