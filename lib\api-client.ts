// API Client utilities for frontend
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

class ApiClient {
  private baseUrl: string

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || ''
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseUrl}/api${endpoint}`
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      })

      const data = await response.json()
      return data
    } catch (error) {
      console.error('API Error:', error)
      return {
        success: false,
        error: 'Network error occurred'
      }
    }
  }

  // Categories
  async getCategories(type?: string) {
    const query = type ? `?type=${type}` : ''
    return this.request(`/categories${query}`)
  }

  // Services
  async getServices(params: {
    category_id?: number
    featured?: boolean
    limit?: number
    search?: string
  } = {}) {
    const query = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        query.append(key, value.toString())
      }
    })
    return this.request(`/services?${query}`)
  }

  async getFeaturedServices(limit = 6) {
    return this.getServices({ featured: true, limit })
  }

  async getService(id: number) {
    return this.request(`/services/${id}`)
  }

  async deleteService(id: number) {
    return this.request(`/services/${id}`, { method: 'DELETE' })
  }

  // Projects
  async getProjects(params: {
    category_id?: number
    featured?: boolean
    limit?: number
    year?: number
  } = {}) {
    const query = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        query.append(key, value.toString())
      }
    })
    return this.request(`/projects?${query}`)
  }

  async getFeaturedProjects(limit = 6) {
    return this.getProjects({ featured: true, limit })
  }

  async getProject(id: number) {
    return this.request(`/projects/${id}`)
  }

  async deleteProject(id: number) {
    return this.request(`/projects/${id}`, { method: 'DELETE' })
  }

  // Articles
  async getArticles(params: {
    category_id?: number
    featured?: boolean
    limit?: number
    popular?: boolean
  } = {}) {
    const query = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        query.append(key, value.toString())
      }
    })
    return this.request(`/articles?${query}`)
  }

  async getFeaturedArticles(limit = 6) {
    return this.getArticles({ featured: true, limit })
  }

  async getPopularArticles(limit = 10) {
    return this.getArticles({ popular: true, limit })
  }

  async getArticle(id: number) {
    return this.request(`/articles/${id}`)
  }

  async deleteArticle(id: number) {
    return this.request(`/articles/${id}`, { method: 'DELETE' })
  }

  // Videos
  async getVideos(params: {
    category_id?: number
    featured?: boolean
    limit?: number
  } = {}) {
    const query = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        query.append(key, value.toString())
      }
    })
    return this.request(`/videos?${query}`)
  }

  async getFeaturedVideos(limit = 6) {
    return this.getVideos({ featured: true, limit })
  }

  async getVideo(id: number) {
    return this.request(`/videos/${id}`)
  }

  async deleteVideo(id: number) {
    return this.request(`/videos/${id}`, { method: 'DELETE' })
  }

  // Experiences
  async getExperiences(params: {
    category_id?: number
    featured?: boolean
    limit?: number
  } = {}) {
    const query = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        query.append(key, value.toString())
      }
    })
    return this.request(`/experiences?${query}`)
  }

  async getFeaturedExperiences(limit = 6) {
    return this.getExperiences({ featured: true, limit })
  }

  async getExperience(id: number) {
    return this.request(`/experiences/${id}`)
  }

  async deleteExperience(id: number) {
    return this.request(`/experiences/${id}`, { method: 'DELETE' })
  }

  // Search
  async search(query: string, type?: 'services' | 'projects' | 'articles' | 'videos' | 'experiences') {
    if (type) {
      return this.request(`/${type}?search=${encodeURIComponent(query)}`)
    }

    // Search all types
    const results = await Promise.all([
      this.request(`/services?search=${encodeURIComponent(query)}&limit=3`),
      this.request(`/projects?search=${encodeURIComponent(query)}&limit=3`),
      this.request(`/articles?search=${encodeURIComponent(query)}&limit=3`),
      this.request(`/videos?search=${encodeURIComponent(query)}&limit=3`),
      this.request(`/experiences?search=${encodeURIComponent(query)}&limit=3`)
    ])

    return {
      success: true,
      data: {
        services: results[0].data || [],
        projects: results[1].data || [],
        articles: results[2].data || [],
        videos: results[3].data || [],
        experiences: results[4].data || []
      }
    }
  }
}

export const apiClient = new ApiClient()
export default apiClient
