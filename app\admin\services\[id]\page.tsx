"use client"

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Calendar, 
  Tag, 
  Star, 
  Clock, 
  DollarSign,
  Loader2,
  AlertTriangle
} from 'lucide-react'
import { apiClient } from '@/lib/api-client'

interface Service {
  id: number
  title: string
  slug: string
  description: string
  content: string
  image: string
  features: string[]
  price_range: string
  duration: string
  category_id: number | null
  category_name?: string
  featured: boolean
  status: 'draft' | 'published' | 'archived'
  created_at: string
  updated_at: string
}

export default function ServiceDetailPage() {
  const params = useParams()
  const router = useRouter()
  const serviceId = parseInt(params.id as string)
  const [service, setService] = useState<Service | null>(null)
  const [loading, setLoading] = useState(true)
  const [deleting, setDeleting] = useState(false)
  const [error, setError] = useState('')
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)

  useEffect(() => {
    fetchService()
  }, [serviceId])

  const fetchService = async () => {
    try {
      const response = await apiClient.getService(serviceId)
      if (response.success && response.data) {
        setService(response.data)
      } else {
        setError('Service not found')
      }
    } catch (error) {
      console.error('Error fetching service:', error)
      setError('Failed to load service')
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async () => {
    if (!service) return
    
    setDeleting(true)
    try {
      const response = await apiClient.deleteService(service.id)
      if (response.success) {
        router.push('/admin/services')
      } else {
        setError(response.error || 'Failed to delete service')
      }
    } catch (error) {
      console.error('Error deleting service:', error)
      setError('Failed to delete service')
    } finally {
      setDeleting(false)
      setShowDeleteConfirm(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Đã xuất bản</Badge>
      case 'draft':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Bản nháp</Badge>
      case 'archived':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Đã lưu trữ</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading service...</span>
        </div>
      </div>
    )
  }

  if (error || !service) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error</h2>
          <p className="text-gray-600 mb-4">{error || 'Service not found'}</p>
          <Link href="/admin/services">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Services
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/admin/services">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-foreground">{service.title}</h1>
              <p className="text-sm text-muted-foreground mt-1">
                Service ID: {service.id} • Created: {new Date(service.created_at).toLocaleDateString()}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Link href={`/admin/services/${service.id}/edit`}>
              <Button>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </Link>
            <Button 
              variant="destructive" 
              onClick={() => setShowDeleteConfirm(true)}
              disabled={deleting}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Delete Confirmation */}
      {showDeleteConfirm && (
        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>Are you sure you want to delete this service? This action cannot be undone.</span>
            <div className="flex space-x-2 ml-4">
              <Button 
                size="sm" 
                variant="outline" 
                onClick={() => setShowDeleteConfirm(false)}
              >
                Cancel
              </Button>
              <Button 
                size="sm" 
                variant="destructive" 
                onClick={handleDelete}
                disabled={deleting}
              >
                {deleting ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                Delete
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                📝 Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold text-sm text-muted-foreground mb-1">Title</h3>
                <p className="text-foreground">{service.title}</p>
              </div>
              <div>
                <h3 className="font-semibold text-sm text-muted-foreground mb-1">Slug</h3>
                <p className="text-foreground font-mono text-sm bg-muted px-2 py-1 rounded">
                  {service.slug}
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-sm text-muted-foreground mb-1">Description</h3>
                <p className="text-foreground">{service.description}</p>
              </div>
            </CardContent>
          </Card>

          {/* Content */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                📄 Content
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <div dangerouslySetInnerHTML={{ __html: service.content }} />
              </div>
            </CardContent>
          </Card>

          {/* Features */}
          {service.features && service.features.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  ⭐ Features
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {service.features.map((feature, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <span className="text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Image */}
          {service.image && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  🖼️ Image
                </CardTitle>
              </CardHeader>
              <CardContent>
                <img 
                  src={service.image} 
                  alt={service.title}
                  className="w-full h-48 object-cover rounded-lg"
                />
              </CardContent>
            </Card>
          )}

          {/* Status & Meta */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                📊 Status & Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status</span>
                {getStatusBadge(service.status)}
              </div>
              
              {service.featured && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Featured</span>
                  <Badge className="bg-blue-100 text-blue-800">
                    <Star className="h-3 w-3 mr-1" />
                    Featured
                  </Badge>
                </div>
              )}

              {service.category_name && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Category</span>
                  <Badge variant="outline">
                    <Tag className="h-3 w-3 mr-1" />
                    {service.category_name}
                  </Badge>
                </div>
              )}

              {service.price_range && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Price Range</span>
                  <Badge variant="outline">
                    <DollarSign className="h-3 w-3 mr-1" />
                    {service.price_range}
                  </Badge>
                </div>
              )}

              {service.duration && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Duration</span>
                  <Badge variant="outline">
                    <Clock className="h-3 w-3 mr-1" />
                    {service.duration}
                  </Badge>
                </div>
              )}

              <div className="pt-4 border-t border-border">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Created</span>
                  <span>{new Date(service.created_at).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center justify-between text-sm mt-1">
                  <span className="text-muted-foreground">Updated</span>
                  <span>{new Date(service.updated_at).toLocaleDateString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                ⚡ Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Link href={`/dich-vu/${service.slug}`} target="_blank">
                <Button variant="outline" className="w-full justify-start">
                  👁️ View Public Page
                </Button>
              </Link>
              <Link href={`/admin/services/${service.id}/edit`}>
                <Button variant="outline" className="w-full justify-start">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Service
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}