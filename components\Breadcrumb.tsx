"use client"

import React, { useEffect, useState } from 'react'
import Link from 'next/link'
import { ChevronRight, Home } from 'lucide-react'

interface BreadcrumbItem {
  label: string
  href: string
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[]
  className?: string
}

const routeNameMap: Record<string, string> = {
  'dich-vu': 'Dịch Vụ',
  'du-an-da-hoan-thien': 'Dự Án <PERSON>',
  'cam-nang': 'Cẩm Nang',
  'chia-se-kinh-nghiem': 'Chia Sẻ Kinh Nghiệm',
  'video-cong-trinh-hoan-thien': 'Video Công Trình Hoàn Thiện',
  'gioi-thieu': 'Giới Thiệu',
  'gui-yeu-cau': 'G<PERSON>i Yêu <PERSON>ầ<PERSON>'
}

export default function Breadcrumb({ items, className = '' }: BreadcrumbProps) {
  const [breadcrumbItems, setBreadcrumbItems] = useState<BreadcrumbItem[]>([{ label: 'Trang chủ', href: '/' }])

  useEffect(() => {
    if (items) {
      setBreadcrumbItems([{ label: 'Trang chủ', href: '/' }, ...items])
    } else {
      // Auto generate breadcrumbs based on current path if no items provided
      const path = window.location.pathname
      const pathSegments = path.split('/').filter(segment => segment)

      if (pathSegments.length > 0) {
        const generatedItems: BreadcrumbItem[] = [{ label: 'Trang chủ', href: '/' }]

        let currentPath = ''
        pathSegments.forEach((segment, index) => {
          currentPath += `/${segment}`
          const label = routeNameMap[segment] || segment
          generatedItems.push({
            label,
            href: currentPath
          })
        })

        setBreadcrumbItems(generatedItems)
      }
    }
  }, [items])

  return (
    <nav aria-label="Breadcrumb" className={`mb-6 ${className}`}>
      <ol className="flex items-center space-x-2">
        {breadcrumbItems.map((item, index) => (
          <React.Fragment key={item.href}>
            <li className="flex items-center">
              {index === 0 ? (
                <Link 
                  href={item.href}
                  className="flex items-center text-gray-500 hover:text-blue-600 transition-colors"
                >
                  <Home className="w-4 h-4 mr-1" />
                  <span className="hidden sm:inline">{item.label}</span>
                </Link>
              ) : (
                <Link 
                  href={item.href}
                  className={`text-sm hover:text-blue-600 transition-colors ${index === breadcrumbItems.length - 1 ? 'font-medium text-blue-600' : 'text-gray-500'}`}
                >
                  {item.label}
                </Link>
              )}
            </li>
            {index < breadcrumbItems.length - 1 && (
              <li>
                <ChevronRight className="w-4 h-4 text-gray-400" />
              </li>
            )}
          </React.Fragment>
        ))}
      </ol>
    </nav>
  )
}
