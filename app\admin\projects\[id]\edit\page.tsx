"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import ProjectForm from '@/components/admin/ProjectForm'
import { apiClient } from '@/lib/api-client'
import { Loader2 } from 'lucide-react'

interface Project {
  id: number
  title: string
  slug: string
  description: string
  content: string
  area: string
  location: string
  year: number
  images: string[]
  category_id: number | null
  featured: boolean
  status: 'draft' | 'published' | 'archived'
}

export default function EditProjectPage() {
  const params = useParams()
  const projectId = parseInt(params.id as string)
  const [project, setProject] = useState<Project | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    fetchProject()
  }, [projectId])

  const fetchProject = async () => {
    try {
      const response = await apiClient.getProject(projectId)
      if (response.success && response.data) {
        setProject(response.data)
      } else {
        setError('Project not found')
      }
    } catch (error) {
      console.error('Error fetching project:', error)
      setError('Failed to load project')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading project...</span>
        </div>
      </div>
    )
  }

  if (error || !project) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error</h2>
          <p className="text-gray-600">{error || 'Project not found'}</p>
        </div>
      </div>
    )
  }

  return (
    <ProjectForm 
      projectId={projectId} 
      initialData={project}
    />
  )
}