"use client"

import { useState } from "react"
import Image from "next/image"

const categories = [
  { id: "all", name: "TẤT CẢ", active: true },
  { id: "living", name: "PHÒNG <PERSON><PERSON><PERSON><PERSON>" },
  { id: "kitchen", name: "PHÒNG BẾP" },
  { id: "bedroom", name: "PHÒNG NGỦ" },
  { id: "bathroom", name: "PHÒNG TẮM" },
  { id: "dining", name: "PHÒNG ĂN" },
]

const galleryItems = [
  {
    id: 1,
    category: "living",
    image: "https://images.unsplash.com/photo-1555041469-a586c61ea9bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
  },
  {
    id: 2,
    category: "kitchen",
    image: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
  },
  {
    id: 3,
    category: "bedroom",
    image: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
  },
  {
    id: 4,
    category: "bathroom",
    image:
      "https://images.unsplash.com/photo-1620626011761-996317b8d101?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
  },
  {
    id: 5,
    category: "dining",
    image:
      "https://images.unsplash.com/photo-1600210492486-724fe5c67fb0?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
  },
  {
    id: 6,
    category: "living",
    image:
      "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
  },
  {
    id: 7,
    category: "kitchen",
    image: "https://images.unsplash.com/photo-1556909085-4c0c4b0c6f8e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
  },
  {
    id: 8,
    category: "bedroom",
    image:
      "https://images.unsplash.com/photo-1631049307264-da0ec9d70304?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300",
  },
]

export default function QuickGallery() {
  const [activeCategory, setActiveCategory] = useState("all")

  const filteredItems =
    activeCategory === "all" ? galleryItems : galleryItems.filter((item) => item.category === activeCategory)

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-[#1F2937] mb-6 tracking-wider">GALLERY NHANH</h2>
          <p className="text-xl text-[#6B7280] max-w-3xl mx-auto">Khám phá các không gian nội thất theo từng khu vực</p>
        </div>

        {/* Category Tabs */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`px-8 py-3 font-semibold transition-all duration-300 border-2 ${
                activeCategory === category.id
                  ? "bg-[#3B82F6] text-white border-[#3B82F6] shadow-lg"
                  : "bg-transparent text-[#1F2937] border-[#E5E7EB] hover:border-[#3B82F6] hover:text-[#3B82F6]"
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {filteredItems.map((item) => (
            <div
              key={item.id}
              className="group relative overflow-hidden rounded-lg shadow-md cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-xl"
            >
              <div className="relative h-64">
                <Image
                  src={item.image || "/placeholder.svg"}
                  alt={`Gallery item ${item.id}`}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300" />
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <span className="bg-white text-[#1F2937] px-4 py-2 rounded-full font-semibold">Xem lớn</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
