import type React from "react"
import type { Metadata } from "next"
import { Be_Vietnam_Pro } from "next/font/google"
import "./globals.css"

const beVietnamPro = Be_Vietnam_Pro({
  subsets: ["latin", "vietnamese"],
  weight: ["300", "400", "500", "600", "700", "800", "900"],
})

export const metadata: Metadata = {
  title: "Kiến Tr<PERSON>c <PERSON>hái <PERSON> - Thiết <PERSON>ế Nộ<PERSON> Thất Cao Cấp",
  description:
    "Chuyên thiết kế và thi công nội thất cao cấp, bếp Nhật Bản nhập khẩu. Hơn 15 năm kinh nghiệm, 2500+ dự án hoàn thành.",
  keywords: "thiết kế nội thất, bếp <PERSON>, nội thất cao cấp, kiến trúc <PERSON>",
  authors: [{ name: "<PERSON>ế<PERSON> <PERSON><PERSON><PERSON>" }],
  openGraph: {
    title: "<PERSON>ến <PERSON>",
    description: "<PERSON><PERSON><PERSON><PERSON> thiết kế và thi công nội thất cao cấp, bếp Nhật Bản nhập khẩu",
    type: "website",
    locale: "vi_VN",
  },
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="vi">
      <body className={beVietnamPro.className}>{children}</body>
    </html>
  )
}
