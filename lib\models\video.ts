import { executeQuery, generateSlug } from '../database'

export interface Video {
  id?: number
  title: string
  slug: string
  description?: string
  video_url?: string
  thumbnail?: string
  duration?: string
  views?: number
  category_id?: number
  featured?: boolean
  status?: 'draft' | 'published' | 'archived'
  created_at?: string
  updated_at?: string
  // Joined fields
  category_name?: string
}

export class VideoModel {
  // Get all videos with optional filters
  static async getAll(filters: {
    category_id?: number
    featured?: boolean
    status?: string
    limit?: number
    offset?: number
  } = {}): Promise<Video[]> {
    let query = `
      SELECT v.*, c.name as category_name
      FROM videos v
      LEFT JOIN categories c ON v.category_id = c.id
      WHERE 1=1
    `
    const params: any[] = []

    if (filters.category_id) {
      query += ' AND v.category_id = ?'
      params.push(filters.category_id)
    }

    if (filters.featured !== undefined) {
      query += ' AND v.featured = ?'
      params.push(filters.featured)
    }

    if (filters.status) {
      query += ' AND v.status = ?'
      params.push(filters.status)
    } else {
      query += ' AND v.status = "published"'
    }

    query += ' ORDER BY v.featured DESC, v.created_at DESC'

    if (filters.limit) {
      query += ' LIMIT ?'
      params.push(filters.limit)
      
      if (filters.offset) {
        query += ' OFFSET ?'
        params.push(filters.offset)
      }
    }

    return executeQuery<Video>(query, params)
  }

  // Get video by ID
  static async getById(id: number): Promise<Video | null> {
    const query = `
      SELECT v.*, c.name as category_name
      FROM videos v
      LEFT JOIN categories c ON v.category_id = c.id
      WHERE v.id = ?
    `
    const results = await executeQuery<Video>(query, [id])
    return results.length > 0 ? results[0] : null
  }

  // Get video by slug
  static async getBySlug(slug: string): Promise<Video | null> {
    const query = `
      SELECT v.*, c.name as category_name
      FROM videos v
      LEFT JOIN categories c ON v.category_id = c.id
      WHERE v.slug = ? AND v.status = 'published'
    `
    const results = await executeQuery<Video>(query, [slug])
    return results.length > 0 ? results[0] : null
  }

  // Create new video
  static async create(data: Omit<Video, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    const slug = data.slug || generateSlug(data.title)
    
    const query = `
      INSERT INTO videos (
        title, slug, description, video_url, thumbnail, 
        duration, views, category_id, featured, status
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
    
    const result = await executeQuery(query, [
      data.title,
      slug,
      data.description || null,
      data.video_url || null,
      data.thumbnail || null,
      data.duration || null,
      data.views || 0,
      data.category_id || null,
      data.featured || false,
      data.status || 'published'
    ])
    
    return (result as any).insertId
  }

  // Update video
  static async update(id: number, data: Partial<Omit<Video, 'id' | 'created_at' | 'updated_at'>>): Promise<boolean> {
    const fields: string[] = []
    const values: any[] = []

    if (data.title !== undefined) {
      fields.push('title = ?')
      values.push(data.title)
    }

    if (data.slug !== undefined) {
      fields.push('slug = ?')
      values.push(data.slug)
    }

    if (data.description !== undefined) {
      fields.push('description = ?')
      values.push(data.description)
    }

    if (data.video_url !== undefined) {
      fields.push('video_url = ?')
      values.push(data.video_url)
    }

    if (data.thumbnail !== undefined) {
      fields.push('thumbnail = ?')
      values.push(data.thumbnail)
    }

    if (data.duration !== undefined) {
      fields.push('duration = ?')
      values.push(data.duration)
    }

    if (data.views !== undefined) {
      fields.push('views = ?')
      values.push(data.views)
    }

    if (data.category_id !== undefined) {
      fields.push('category_id = ?')
      values.push(data.category_id)
    }

    if (data.featured !== undefined) {
      fields.push('featured = ?')
      values.push(data.featured)
    }

    if (data.status !== undefined) {
      fields.push('status = ?')
      values.push(data.status)
    }

    if (fields.length === 0) {
      return false
    }

    values.push(id)

    const query = `
      UPDATE videos 
      SET ${fields.join(', ')}
      WHERE id = ?
    `
    
    const result = await executeQuery(query, values)
    return (result as any).affectedRows > 0
  }

  // Delete video
  static async delete(id: number): Promise<boolean> {
    const query = `
      DELETE FROM videos 
      WHERE id = ?
    `
    const result = await executeQuery(query, [id])
    return (result as any).affectedRows > 0
  }

  // Get featured videos
  static async getFeatured(limit: number = 6): Promise<Video[]> {
    return this.getAll({ featured: true, limit })
  }

  // Search videos
  static async search(searchTerm: string, limit: number = 10): Promise<Video[]> {
    const query = `
      SELECT v.*, c.name as category_name
      FROM videos v
      LEFT JOIN categories c ON v.category_id = c.id
      WHERE v.status = 'published' 
        AND (v.title LIKE ? OR v.description LIKE ?)
      ORDER BY v.featured DESC, v.created_at DESC
      LIMIT ?
    `
    
    const searchPattern = `%${searchTerm}%`
    return executeQuery<Video>(query, [searchPattern, searchPattern, limit])
  }

  // Increment view count
  static async incrementViews(id: number): Promise<boolean> {
    const query = `
      UPDATE videos 
      SET views = views + 1
      WHERE id = ?
    `
    const result = await executeQuery(query, [id])
    return (result as any).affectedRows > 0
  }

  // Get popular videos
  static async getPopular(limit: number = 10): Promise<Video[]> {
    const query = `
      SELECT v.*, c.name as category_name
      FROM videos v
      LEFT JOIN categories c ON v.category_id = c.id
      WHERE v.status = 'published'
      ORDER BY v.views DESC, v.created_at DESC
      LIMIT ?
    `
    
    return executeQuery<Video>(query, [limit])
  }
}
