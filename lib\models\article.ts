import { executeQuery, generateSlug } from '../database'

export interface Article {
  id?: number
  title: string
  slug: string
  excerpt?: string
  content?: string
  image?: string
  author?: string
  category_id?: number
  featured?: boolean
  views?: number
  status?: 'draft' | 'published' | 'archived'
  published_date?: string
  created_at?: string
  updated_at?: string
  // Joined fields
  category_name?: string
}

export class ArticleModel {
  // Get all articles with optional filters
  static async getAll(filters: {
    category_id?: number
    featured?: boolean
    status?: string
    limit?: number
    offset?: number
  } = {}): Promise<Article[]> {
    let query = `
      SELECT a.*, c.name as category_name
      FROM articles a
      LEFT JOIN categories c ON a.category_id = c.id
      WHERE 1=1
    `
    const params: any[] = []

    if (filters.category_id) {
      query += ' AND a.category_id = ?'
      params.push(filters.category_id)
    }

    if (filters.featured !== undefined) {
      query += ' AND a.featured = ?'
      params.push(filters.featured)
    }

    if (filters.status) {
      query += ' AND a.status = ?'
      params.push(filters.status)
    } else {
      query += ' AND a.status = "published"'
    }

    query += ' ORDER BY a.featured DESC, a.published_date DESC, a.created_at DESC'

    if (filters.limit) {
      query += ' LIMIT ?'
      params.push(filters.limit)
      
      if (filters.offset) {
        query += ' OFFSET ?'
        params.push(filters.offset)
      }
    }

    return executeQuery<Article>(query, params)
  }

  // Get article by ID
  static async getById(id: number): Promise<Article | null> {
    const query = `
      SELECT a.*, c.name as category_name
      FROM articles a
      LEFT JOIN categories c ON a.category_id = c.id
      WHERE a.id = ?
    `
    const results = await executeQuery<Article>(query, [id])
    return results.length > 0 ? results[0] : null
  }

  // Get article by slug
  static async getBySlug(slug: string): Promise<Article | null> {
    const query = `
      SELECT a.*, c.name as category_name
      FROM articles a
      LEFT JOIN categories c ON a.category_id = c.id
      WHERE a.slug = ? AND a.status = 'published'
    `
    const results = await executeQuery<Article>(query, [slug])
    return results.length > 0 ? results[0] : null
  }

  // Create new article
  static async create(data: Omit<Article, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    const slug = data.slug || generateSlug(data.title)
    
    const query = `
      INSERT INTO articles (
        title, slug, excerpt, content, image, author, 
        category_id, featured, views, status, published_date
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
    
    const result = await executeQuery(query, [
      data.title,
      slug,
      data.excerpt || null,
      data.content || null,
      data.image || null,
      data.author || null,
      data.category_id || null,
      data.featured || false,
      data.views || 0,
      data.status || 'published',
      data.published_date || new Date().toISOString()
    ])
    
    return (result as any).insertId
  }

  // Update article
  static async update(id: number, data: Partial<Omit<Article, 'id' | 'created_at' | 'updated_at'>>): Promise<boolean> {
    const fields: string[] = []
    const values: any[] = []

    if (data.title !== undefined) {
      fields.push('title = ?')
      values.push(data.title)
    }

    if (data.slug !== undefined) {
      fields.push('slug = ?')
      values.push(data.slug)
    }

    if (data.excerpt !== undefined) {
      fields.push('excerpt = ?')
      values.push(data.excerpt)
    }

    if (data.content !== undefined) {
      fields.push('content = ?')
      values.push(data.content)
    }

    if (data.image !== undefined) {
      fields.push('image = ?')
      values.push(data.image)
    }

    if (data.author !== undefined) {
      fields.push('author = ?')
      values.push(data.author)
    }

    if (data.category_id !== undefined) {
      fields.push('category_id = ?')
      values.push(data.category_id)
    }

    if (data.featured !== undefined) {
      fields.push('featured = ?')
      values.push(data.featured)
    }

    if (data.views !== undefined) {
      fields.push('views = ?')
      values.push(data.views)
    }

    if (data.status !== undefined) {
      fields.push('status = ?')
      values.push(data.status)
    }

    if (data.published_date !== undefined) {
      fields.push('published_date = ?')
      values.push(data.published_date)
    }

    if (fields.length === 0) {
      return false
    }

    values.push(id)

    const query = `
      UPDATE articles 
      SET ${fields.join(', ')}
      WHERE id = ?
    `
    
    const result = await executeQuery(query, values)
    return (result as any).affectedRows > 0
  }

  // Delete article
  static async delete(id: number): Promise<boolean> {
    const query = `
      DELETE FROM articles 
      WHERE id = ?
    `
    const result = await executeQuery(query, [id])
    return (result as any).affectedRows > 0
  }

  // Get featured articles
  static async getFeatured(limit: number = 6): Promise<Article[]> {
    return this.getAll({ featured: true, limit })
  }

  // Search articles
  static async search(searchTerm: string, limit: number = 10): Promise<Article[]> {
    const query = `
      SELECT a.*, c.name as category_name
      FROM articles a
      LEFT JOIN categories c ON a.category_id = c.id
      WHERE a.status = 'published' 
        AND (a.title LIKE ? OR a.excerpt LIKE ? OR a.content LIKE ?)
      ORDER BY a.featured DESC, a.published_date DESC, a.created_at DESC
      LIMIT ?
    `
    
    const searchPattern = `%${searchTerm}%`
    return executeQuery<Article>(query, [searchPattern, searchPattern, searchPattern, limit])
  }

  // Increment view count
  static async incrementViews(id: number): Promise<boolean> {
    const query = `
      UPDATE articles 
      SET views = views + 1
      WHERE id = ?
    `
    const result = await executeQuery(query, [id])
    return (result as any).affectedRows > 0
  }

  // Get related articles
  static async getRelated(articleId: number, categoryId: number, limit: number = 4): Promise<Article[]> {
    const query = `
      SELECT a.*, c.name as category_name
      FROM articles a
      LEFT JOIN categories c ON a.category_id = c.id
      WHERE a.status = 'published' 
        AND a.id != ?
        AND a.category_id = ?
      ORDER BY a.published_date DESC
      LIMIT ?
    `
    
    return executeQuery<Article>(query, [articleId, categoryId, limit])
  }

  // Get popular articles
  static async getPopular(limit: number = 10): Promise<Article[]> {
    const query = `
      SELECT a.*, c.name as category_name
      FROM articles a
      LEFT JOIN categories c ON a.category_id = c.id
      WHERE a.status = 'published'
      ORDER BY a.views DESC, a.published_date DESC
      LIMIT ?
    `
    
    return executeQuery<Article>(query, [limit])
  }
}
