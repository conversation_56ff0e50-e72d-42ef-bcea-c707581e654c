"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import ServiceForm from '@/components/admin/ServiceForm'
import { apiClient } from '@/lib/api-client'
import { Loader2 } from 'lucide-react'

interface Service {
  id: number
  title: string
  slug: string
  description: string
  content: string
  image: string
  features: string[]
  price_range: string
  duration: string
  category_id: number | null
  featured: boolean
  status: 'draft' | 'published' | 'archived'
}

export default function EditServicePage() {
  const params = useParams()
  const serviceId = parseInt(params.id as string)
  const [service, setService] = useState<Service | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    fetchService()
  }, [serviceId])

  const fetchService = async () => {
    try {
      const response = await apiClient.getService(serviceId)
      if (response.success && response.data) {
        setService(response.data)
      } else {
        setError('Service not found')
      }
    } catch (error) {
      console.error('Error fetching service:', error)
      setError('Failed to load service')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading service...</span>
        </div>
      </div>
    )
  }

  if (error || !service) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error</h2>
          <p className="text-gray-600">{error || 'Service not found'}</p>
        </div>
      </div>
    )
  }

  return (
    <ServiceForm 
      serviceId={serviceId} 
      initialData={service}
    />
  )
}