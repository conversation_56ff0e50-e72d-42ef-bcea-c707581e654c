import Head from 'next/head'

interface MetaTagsProps {
  title: string
  description: string
  keywords?: string
  ogImage?: string
  ogUrl?: string
  ogType?: 'website' | 'article' | 'profile'
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player'
}

export default function MetaTags({
  title,
  description,
  keywords,
  ogImage = '/images/og-default.jpg',
  ogUrl,
  ogType = 'website',
  twitterCard = 'summary_large_image'
}: MetaTagsProps) {
  const siteTitle = title ? `${title} | Kiến Trúc S<PERSON> Thái Vũ` : 'Kiến Trúc Sư Thái Vũ - Thiết kế nội thất chuyên nghiệp'

  return (
    <Head>
      <title>{siteTitle}</title>
      <meta name="description" content={description} />
      {keywords && <meta name="keywords" content={keywords} />}

      {/* Open Graph */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={ogType} />
      {ogImage && <meta property="og:image" content={ogImage} />}
      {ogUrl && <meta property="og:url" content={ogUrl} />}
      <meta property="og:site_name" content="Kiến Trúc Sư Thái Vũ" />

      {/* Twitter Card */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      {ogImage && <meta name="twitter:image" content={ogImage} />}

      <link rel="canonical" href={ogUrl} />
    </Head>
  )
}
