"use client"

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Upload, X, Plus, Save, ArrowLeft } from 'lucide-react'
import { motion, AnimatePresence } from "framer-motion"
import { apiClient } from '@/lib/api-client'
import { uploadFile, validateFile } from '@/lib/upload'
import {
  fadeInUp,
  staggerContainer,
  staggerItem,
  buttonPress,
  getMotionProps
} from "@/lib/motion"

interface Category {
  id: number
  name: string
}

interface ServiceFormData {
  title: string
  slug: string
  description: string
  content: string
  image: string
  features: string[]
  price_range: string
  duration: string
  category_id: number | null
  featured: boolean
  status: 'draft' | 'published' | 'archived'
}

interface ServiceFormProps {
  serviceId?: number
  initialData?: Partial<ServiceFormData>
}

export default function ServiceForm({ serviceId, initialData }: ServiceFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState('')
  const [categories, setCategories] = useState<Category[]>([])
  const [newFeature, setNewFeature] = useState('')

  const [formData, setFormData] = useState<ServiceFormData>({
    title: '',
    slug: '',
    description: '',
    content: '',
    image: '',
    features: [],
    price_range: '',
    duration: '',
    category_id: null,
    featured: false,
    status: 'draft',
    ...initialData
  })

  useEffect(() => {
    fetchCategories()
  }, [])

  const fetchCategories = async () => {
    try {
      const response = await apiClient.getCategories('services')
      if (response.success) {
        setCategories(response.data || [])
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
    }
  }

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: generateSlug(title)
    }))
  }

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    const validation = validateFile(file)
    if (!validation.valid) {
      setError(validation.error || 'Invalid file')
      return
    }

    setUploading(true)
    setError('')

    try {
      const result = await uploadFile(file, 'services')
      if (result.success && result.data) {
        setFormData(prev => ({ ...prev, image: result.data!.url }))
      } else {
        setError(result.error || 'Upload failed')
      }
    } catch (error) {
      setError('Upload failed')
    } finally {
      setUploading(false)
    }
  }

  const addFeature = () => {
    if (newFeature.trim()) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()]
      }))
      setNewFeature('')
    }
  }

  const removeFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const response = serviceId
        ? await fetch(`/api/services/${serviceId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formData)
          })
        : await fetch('/api/services', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formData)
          })

      const result = await response.json()

      if (result.success) {
        router.push('/admin/services')
      } else {
        setError(result.error || 'Failed to save service')
      }
    } catch (error) {
      setError('Failed to save service')
    } finally {
      setLoading(false)
    }
  }

  return (
    <motion.div
      className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6"
      {...getMotionProps(fadeInUp)}
    >
      {/* Header with back button */}
      <motion.div
        className="mb-8"
        {...getMotionProps(fadeInUp)}
      >
        <div className="flex items-center space-x-4 mb-4">
          <motion.button
            onClick={() => window.history.back()}
            className="flex items-center space-x-2 text-muted-foreground hover:text-foreground transition-colors duration-200 p-2 rounded-lg hover:bg-accent/50"
            {...buttonPress}
            whileHover={{ x: -2 }}
          >
            <ArrowLeft className="h-5 w-5" />
            <span>Quay lại</span>
          </motion.button>
        </div>

        <motion.h1
          className="text-3xl font-bold text-foreground flex items-center space-x-3"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <motion.span
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ duration: 0.2 }}
          >
            {serviceId ? '✏️' : '➕'}
          </motion.span>
          <span>{serviceId ? 'Chỉnh Sửa Dịch Vụ' : 'Tạo Dịch Vụ Mới'}</span>
        </motion.h1>
        <motion.p
          className="mt-2 text-sm text-muted-foreground"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {serviceId ? 'Cập nhật thông tin dịch vụ thiết kế nội thất' : 'Thêm dịch vụ thiết kế nội thất mới vào hệ thống'}
        </motion.p>
      </motion.div>

      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            <Alert variant="destructive" className="mb-6 border-destructive/20 bg-destructive/5">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </motion.div>
        )}
      </AnimatePresence>

      <motion.form
        onSubmit={handleSubmit}
        className="space-y-6"
        {...getMotionProps(staggerContainer)}
      >
        <motion.div {...getMotionProps(staggerItem)}>
          <Card className="border-border shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-accent/5 to-accent/10 border-b border-border/50">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
              >
                <CardTitle className="text-foreground flex items-center space-x-2">
                  <motion.span
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    📝
                  </motion.span>
                  <span>Thông Tin Cơ Bản</span>
                </CardTitle>
              </motion.div>
            </CardHeader>
            <CardContent className="space-y-6 p-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="space-y-2"
              >
                <Label htmlFor="title" className="text-sm font-medium text-foreground">
                  Title <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleTitleChange(e.target.value)}
                  required
                  className="transition-all duration-200 focus:ring-2 focus:ring-primary/20 focus:border-primary"
                  placeholder="Nhập tên dịch vụ..."
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="space-y-2"
              >
                <Label htmlFor="slug" className="text-sm font-medium text-foreground">
                  Slug
                </Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                  className="transition-all duration-200 focus:ring-2 focus:ring-primary/20 focus:border-primary"
                  placeholder="URL slug tự động tạo..."
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="space-y-2"
              >
                <Label htmlFor="description" className="text-sm font-medium text-foreground">
                  Mô tả ngắn
                </Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="transition-all duration-200 focus:ring-2 focus:ring-primary/20 focus:border-primary resize-none"
                  placeholder="Mô tả ngắn gọn về dịch vụ..."
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="space-y-2"
              >
                <Label htmlFor="content" className="text-sm font-medium text-foreground">
                  Nội dung chi tiết
                </Label>
                <Textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  rows={8}
                  className="transition-all duration-200 focus:ring-2 focus:ring-primary/20 focus:border-primary resize-none"
                  placeholder="Nội dung chi tiết về dịch vụ..."
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="grid grid-cols-1 md:grid-cols-2 gap-4"
              >
                <div className="space-y-2">
                  <Label htmlFor="price_range" className="text-sm font-medium text-foreground">
                    Khoảng giá
                  </Label>
                  <Input
                    id="price_range"
                    value={formData.price_range}
                    onChange={(e) => setFormData(prev => ({ ...prev, price_range: e.target.value }))}
                    className="transition-all duration-200 focus:ring-2 focus:ring-primary/20 focus:border-primary"
                    placeholder="VD: 50-200 triệu"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="duration" className="text-sm font-medium text-foreground">
                    Thời gian thực hiện
                  </Label>
                  <Input
                    id="duration"
                    value={formData.duration}
                    onChange={(e) => setFormData(prev => ({ ...prev, duration: e.target.value }))}
                    className="transition-all duration-200 focus:ring-2 focus:ring-primary/20 focus:border-primary"
                    placeholder="VD: 2-4 tuần"
                  />
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="space-y-2"
              >
                <Label htmlFor="category" className="text-sm font-medium text-foreground">
                  Danh mục
                </Label>
                <Select
                  value={formData.category_id?.toString() || ''}
                  onValueChange={(value) => setFormData(prev => ({ 
                    ...prev, 
                    category_id: value ? parseInt(value) : null 
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn danh mục..." />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Features Section */}
        <motion.div {...getMotionProps(staggerItem)}>
          <Card className="border-border shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-accent/5 to-accent/10 border-b border-border/50">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
              >
                <CardTitle className="text-foreground flex items-center space-x-2">
                  <motion.span
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    ⭐
                  </motion.span>
                  <span>Tính Năng Nổi Bật</span>
                </CardTitle>
              </motion.div>
            </CardHeader>
            <CardContent className="space-y-4 p-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="flex space-x-2"
              >
                <Input
                  value={newFeature}
                  onChange={(e) => setNewFeature(e.target.value)}
                  placeholder="Nhập tính năng mới..."
                  className="flex-1 transition-all duration-200 focus:ring-2 focus:ring-primary/20 focus:border-primary"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
                />
                <Button
                  type="button"
                  onClick={addFeature}
                  className="px-4 py-2 bg-primary hover:bg-primary/90 transition-all duration-200"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="space-y-2"
              >
                {formData.features.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center justify-between p-3 bg-accent/10 rounded-lg border border-border/50"
                  >
                    <span className="text-sm text-foreground">{feature}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFeature(index)}
                      className="h-8 w-8 p-0 text-destructive hover:bg-destructive/10 transition-colors duration-200"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </motion.div>
                ))}
                {formData.features.length === 0 && (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    Chưa có tính năng nào. Thêm tính năng để làm nổi bật dịch vụ.
                  </p>
                )}
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Image Section */}
        <motion.div {...getMotionProps(staggerItem)}>
          <Card className="border-border shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-accent/5 to-accent/10 border-b border-border/50">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
              >
                <CardTitle className="text-foreground flex items-center space-x-2">
                  <motion.span
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    🖼️
                  </motion.span>
                  <span>Hình Ảnh</span>
                </CardTitle>
              </motion.div>
            </CardHeader>
            <CardContent className="space-y-4 p-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="space-y-2"
              >
                <Label htmlFor="image" className="text-sm font-medium text-foreground">
                  Upload hình ảnh
                </Label>
                <Input
                  id="image"
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  disabled={uploading}
                  className="transition-all duration-200 focus:ring-2 focus:ring-primary/20 focus:border-primary"
                />
                {uploading && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="flex items-center mt-2 text-sm text-muted-foreground"
                  >
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Đang tải lên...
                  </motion.div>
                )}
              </motion.div>

              {formData.image && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.2 }}
                  className="relative inline-block"
                >
                  <img
                    src={formData.image}
                    alt="Service image"
                    className="h-32 w-32 object-cover rounded-lg border border-border shadow-sm"
                  />
                  <motion.button
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, image: '' }))}
                    className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground rounded-full p-1 shadow-md hover:bg-destructive/90 transition-colors duration-200"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <X className="h-4 w-4" />
                  </motion.button>
                </motion.div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Settings Section */}
        <motion.div {...getMotionProps(staggerItem)}>
          <Card className="border-border shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-accent/5 to-accent/10 border-b border-border/50">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
              >
                <CardTitle className="text-foreground flex items-center space-x-2">
                  <motion.span
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    ⚙️
                  </motion.span>
                  <span>Cài Đặt</span>
                </CardTitle>
              </motion.div>
            </CardHeader>
            <CardContent className="space-y-6 p-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="flex items-center justify-between p-4 bg-accent/5 rounded-lg border border-border/30"
              >
                <div className="space-y-1">
                  <Label htmlFor="featured" className="text-sm font-medium text-foreground">
                    Dịch vụ nổi bật
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Hiển thị dịch vụ này ở vị trí nổi bật trên trang chủ
                  </p>
                </div>
                <Switch
                  id="featured"
                  checked={formData.featured}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, featured: checked }))}
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="space-y-2"
              >
                <Label htmlFor="status" className="text-sm font-medium text-foreground">
                  Trạng thái
                </Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: 'draft' | 'published' | 'archived') => 
                    setFormData(prev => ({ ...prev, status: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                        <span>Bản nháp</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="published">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                        <span>Đã xuất bản</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="archived">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-gray-500"></div>
                        <span>Đã lưu trữ</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          className="flex justify-end space-x-4 pt-6 border-t border-border"
          {...getMotionProps(staggerItem)}
        >
          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              className="px-6 py-2 transition-all duration-200 hover:bg-accent/50"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Hủy bỏ
            </Button>
          </motion.div>

          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-primary hover:bg-primary/90 transition-all duration-200 shadow-md hover:shadow-lg"
            >
              {loading ? (
                <motion.div
                  className="flex items-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                >
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Đang lưu...
                </motion.div>
              ) : (
                <motion.div
                  className="flex items-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                >
                  <Save className="mr-2 h-4 w-4" />
                  {serviceId ? 'Cập nhật dịch vụ' : 'Tạo dịch vụ'}
                </motion.div>
              )}
            </Button>
          </motion.div>
        </motion.div>
      </motion.form>
    </motion.div>
  )
}
