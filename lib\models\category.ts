import { executeQuery, generateSlug } from '../database'

export interface Category {
  id?: number
  name: string
  slug: string
  description?: string
  created_at?: string
  updated_at?: string
}

export class CategoryModel {
  // Get all categories
  static async getAll(): Promise<Category[]> {
    const query = `
      SELECT * FROM categories 
      ORDER BY name ASC
    `
    return executeQuery<Category>(query)
  }

  // Get category by ID
  static async getById(id: number): Promise<Category | null> {
    const query = `
      SELECT * FROM categories 
      WHERE id = ?
    `
    const results = await executeQuery<Category>(query, [id])
    return results.length > 0 ? results[0] : null
  }

  // Get category by slug
  static async getBySlug(slug: string): Promise<Category | null> {
    const query = `
      SELECT * FROM categories 
      WHERE slug = ?
    `
    const results = await executeQuery<Category>(query, [slug])
    return results.length > 0 ? results[0] : null
  }

  // Create new category
  static async create(data: Omit<Category, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    const slug = data.slug || generateSlug(data.name)
    
    const query = `
      INSERT INTO categories (name, slug, description)
      VALUES (?, ?, ?)
    `
    const result = await executeQuery(query, [
      data.name,
      slug,
      data.description || null
    ])
    
    return (result as any).insertId
  }

  // Update category
  static async update(id: number, data: Partial<Omit<Category, 'id' | 'created_at' | 'updated_at'>>): Promise<boolean> {
    const fields: string[] = []
    const values: any[] = []

    if (data.name !== undefined) {
      fields.push('name = ?')
      values.push(data.name)
    }

    if (data.slug !== undefined) {
      fields.push('slug = ?')
      values.push(data.slug)
    }

    if (data.description !== undefined) {
      fields.push('description = ?')
      values.push(data.description)
    }

    if (fields.length === 0) {
      return false
    }

    values.push(id)

    const query = `
      UPDATE categories 
      SET ${fields.join(', ')}
      WHERE id = ?
    `
    
    const result = await executeQuery(query, values)
    return (result as any).affectedRows > 0
  }

  // Delete category
  static async delete(id: number): Promise<boolean> {
    const query = `
      DELETE FROM categories 
      WHERE id = ?
    `
    const result = await executeQuery(query, [id])
    return (result as any).affectedRows > 0
  }

  // Get categories with count of related items
  static async getWithCounts(type: 'services' | 'projects' | 'articles' | 'videos' | 'experiences'): Promise<(Category & { count: number })[]> {
    const query = `
      SELECT c.*, COUNT(t.id) as count
      FROM categories c
      LEFT JOIN ${type} t ON c.id = t.category_id AND t.status = 'published'
      GROUP BY c.id
      ORDER BY c.name ASC
    `
    return executeQuery<Category & { count: number }>(query)
  }
}
