import { executeQuery, generateSlug } from '../database'

export interface Service {
  id?: number
  title: string
  slug: string
  description?: string
  content?: string
  image?: string
  features?: string[] // Will be stored as JSON
  price_range?: string
  duration?: string
  category_id?: number
  featured?: boolean
  status?: 'draft' | 'published' | 'archived'
  created_at?: string
  updated_at?: string
  // Joined fields
  category_name?: string
}

export class ServiceModel {
  // Get all services with optional filters
  static async getAll(filters: {
    category_id?: number
    featured?: boolean
    status?: string
    limit?: number
    offset?: number
  } = {}): Promise<Service[]> {
    let query = `
      SELECT s.*, c.name as category_name
      FROM services s
      LEFT JOIN categories c ON s.category_id = c.id
      WHERE 1=1
    `
    const params: any[] = []

    if (filters.category_id) {
      query += ' AND s.category_id = ?'
      params.push(filters.category_id)
    }

    if (filters.featured !== undefined) {
      query += ' AND s.featured = ?'
      params.push(filters.featured)
    }

    if (filters.status) {
      query += ' AND s.status = ?'
      params.push(filters.status)
    } else {
      query += ' AND s.status = "published"'
    }

    query += ' ORDER BY s.featured DESC, s.created_at DESC'

    if (filters.limit) {
      query += ' LIMIT ?'
      params.push(filters.limit)

      if (filters.offset) {
        query += ' OFFSET ?'
        params.push(filters.offset)
      }
    }

    const results = await executeQuery<Service>(query, params)

    // Parse JSON features
    return results.map(service => ({
      ...service,
      features: service.features ? JSON.parse(service.features as any) : []
    }))
  }

  // Get service by ID
  static async getById(id: number): Promise<Service | null> {
    const query = `
      SELECT s.*, c.name as category_name
      FROM services s
      LEFT JOIN categories c ON s.category_id = c.id
      WHERE s.id = ?
    `
    const results = await executeQuery<Service>(query, [id])

    if (results.length === 0) return null

    const service = results[0]
    return {
      ...service,
      features: service.features ? JSON.parse(service.features as any) : []
    }
  }

  // Get service by slug
  static async getBySlug(slug: string): Promise<Service | null> {
    const query = `
      SELECT s.*, c.name as category_name
      FROM services s
      LEFT JOIN categories c ON s.category_id = c.id
      WHERE s.slug = ? AND s.status = 'published'
    `
    const results = await executeQuery<Service>(query, [slug])

    if (results.length === 0) return null

    const service = results[0]
    return {
      ...service,
      features: service.features ? JSON.parse(service.features as any) : []
    }
  }

  // Create new service
  static async create(data: Omit<Service, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    const slug = data.slug || generateSlug(data.title)

    const query = `
      INSERT INTO services (
        title, slug, description, content, image, features, 
        price_range, duration, category_id, featured, status
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `

    const result = await executeQuery(query, [
      data.title,
      slug,
      data.description || null,
      data.content || null,
      data.image || null,
      data.features ? JSON.stringify(data.features) : null,
      data.price_range || null,
      data.duration || null,
      data.category_id || null,
      data.featured || false,
      data.status || 'published'
    ])

    return (result as any).insertId
  }

  // Update service
  static async update(id: number, data: Partial<Omit<Service, 'id' | 'created_at' | 'updated_at'>>): Promise<boolean> {
    const fields: string[] = []
    const values: any[] = []

    if (data.title !== undefined) {
      fields.push('title = ?')
      values.push(data.title)
    }

    if (data.slug !== undefined) {
      fields.push('slug = ?')
      values.push(data.slug)
    }

    if (data.description !== undefined) {
      fields.push('description = ?')
      values.push(data.description)
    }

    if (data.content !== undefined) {
      fields.push('content = ?')
      values.push(data.content)
    }

    if (data.image !== undefined) {
      fields.push('image = ?')
      values.push(data.image)
    }

    if (data.features !== undefined) {
      fields.push('features = ?')
      values.push(JSON.stringify(data.features))
    }

    if (data.price_range !== undefined) {
      fields.push('price_range = ?')
      values.push(data.price_range)
    }

    if (data.duration !== undefined) {
      fields.push('duration = ?')
      values.push(data.duration)
    }

    if (data.category_id !== undefined) {
      fields.push('category_id = ?')
      values.push(data.category_id)
    }

    if (data.featured !== undefined) {
      fields.push('featured = ?')
      values.push(data.featured)
    }

    if (data.status !== undefined) {
      fields.push('status = ?')
      values.push(data.status)
    }

    if (fields.length === 0) {
      return false
    }

    values.push(id)

    const query = `
      UPDATE services 
      SET ${fields.join(', ')}
      WHERE id = ?
    `

    const result = await executeQuery(query, values)
    return (result as any).affectedRows > 0
  }

  // Delete service
  static async delete(id: number): Promise<boolean> {
    const query = `
      DELETE FROM services
      WHERE id = ?
    `
    const result = await executeQuery(query, [id])
    return (result as any).affectedRows > 0
  }

  // Get count of services with filters
  static async getCount(filters: {
    category_id?: number
    featured?: boolean
    status?: string
    search?: string
  } = {}): Promise<number> {
    let query = `
      SELECT COUNT(*) as count
      FROM services s
      WHERE 1=1
    `
    const params: any[] = []

    if (filters.category_id) {
      query += ' AND s.category_id = ?'
      params.push(filters.category_id)
    }

    if (filters.featured !== undefined) {
      query += ' AND s.featured = ?'
      params.push(filters.featured)
    }

    if (filters.status) {
      query += ' AND s.status = ?'
      params.push(filters.status)
    } else {
      query += ' AND s.status = "published"'
    }

    if (filters.search) {
      query += ' AND (s.title LIKE ? OR s.description LIKE ?)'
      const searchTerm = `%${filters.search}%`
      params.push(searchTerm, searchTerm)
    }

    const result = await executeQuery<{ count: number }>(query, params)
    return result[0]?.count || 0
  }

  // Get featured services
  static async getFeatured(limit: number = 6): Promise<Service[]> {
    return this.getAll({ featured: true, limit })
  }

  // Search services
  static async search(searchTerm: string, limit: number = 10): Promise<Service[]> {
    const query = `
      SELECT s.*, c.name as category_name
      FROM services s
      LEFT JOIN categories c ON s.category_id = c.id
      WHERE s.status = 'published' 
        AND (s.title LIKE ? OR s.description LIKE ? OR s.content LIKE ?)
      ORDER BY s.featured DESC, s.created_at DESC
      LIMIT ?
    `

    const searchPattern = `%${searchTerm}%`
    const results = await executeQuery<Service>(query, [searchPattern, searchPattern, searchPattern, limit])

    return results.map(service => ({
      ...service,
      features: service.features ? JSON.parse(service.features as any) : []
    }))
  }

  // Change service status
  static async changeStatus(id: number, status: 'draft' | 'published' | 'archived'): Promise<boolean> {
    const query = `
      UPDATE services 
      SET status = ?
      WHERE id = ?
    `
    const result = await executeQuery(query, [status, id])
    return (result as any).affectedRows > 0
  }

  // Publish service
  static async publish(id: number): Promise<boolean> {
    return this.changeStatus(id, 'published')
  }

  // Archive service
  static async archive(id: number): Promise<boolean> {
    return this.changeStatus(id, 'archived')
  }

  // Draft service
  static async draft(id: number): Promise<boolean> {
    return this.changeStatus(id, 'draft')
  }
}
