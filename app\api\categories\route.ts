import { NextRequest, NextResponse } from 'next/server'
import { CategoryModel } from '@/lib/models/category'

// GET /api/categories
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') as 'services' | 'projects' | 'articles' | 'videos' | 'experiences' | null

    let categories
    if (type) {
      categories = await CategoryModel.getWithCounts(type)
    } else {
      categories = await CategoryModel.getAll()
    }

    return NextResponse.json({
      success: true,
      data: categories
    })
  } catch (error) {
    console.error('Categories API Error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch categories'
      },
      { status: 500 }
    )
  }
}

// POST /api/categories
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, slug, description } = body

    if (!name) {
      return NextResponse.json(
        {
          success: false,
          error: 'Name is required'
        },
        { status: 400 }
      )
    }

    const categoryId = await CategoryModel.create({
      name,
      slug,
      description
    })

    const category = await CategoryModel.getById(categoryId)

    return NextResponse.json({
      success: true,
      data: category
    }, { status: 201 })
  } catch (error) {
    console.error('Categories API Error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create category'
      },
      { status: 500 }
    )
  }
}

// PUT /api/categories (for bulk update if needed)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    // Implementation for bulk update if needed
    return NextResponse.json({
      success: false,
      error: 'Bulk update not implemented'
    }, { status: 501 })
  } catch (error) {
    console.error('Categories API Error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update categories'
      },
      { status: 500 }
    )
  }
}
