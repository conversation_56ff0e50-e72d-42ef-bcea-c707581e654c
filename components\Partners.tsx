"use client"

const partners = [
  {
    id: 1,
    name: "<PERSON> Cường",
    logo: "https://images.unsplash.com/photo-1599305445671-ac291c95aaa9?ixlib=rb-4.0.3&auto=format&fit=crop&w=120&h=60",
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    logo: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=120&h=60",
  },
  {
    id: 3,
    name: "<PERSON><PERSON><PERSON>",
    logo: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=120&h=60",
  },
  {
    id: 4,
    name: "<PERSON><PERSON><PERSON>",
    logo: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=120&h=60",
  },
  {
    id: 5,
    name: "Takara Standard",
    logo: "https://images.unsplash.com/photo-1555041469-a586c61ea9bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=120&h=60",
  },
  {
    id: 6,
    name: "Panasonic",
    logo: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=120&h=60",
  },
  {
    id: 7,
    name: "Toto",
    logo: "https://images.unsplash.com/photo-1620626011761-996317b8d101?ixlib=rb-4.0.3&auto=format&fit=crop&w=120&h=60",
  },
  {
    id: 8,
    name: "Kohler",
    logo: "https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=120&h=60",
  },
]

export default function Partners() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-[#1F2937] mb-6 tracking-wider">ĐỐI TÁC CỦA CHÚNG TÔI</h2>
          <p className="text-xl text-[#6B7280] max-w-3xl mx-auto">
            Hợp tác cùng những thương hiệu uy tín hàng đầu thế giới
          </p>
        </div>

        {/* Marquee Effect */}
        <div className="overflow-hidden">
          <div className="flex animate-marquee space-x-16">
            {[...partners, ...partners].map((partner, index) => (
              <div
                key={`${partner.id}-${index}`}
                className="flex-shrink-0 grayscale hover:grayscale-0 transition-all duration-300 cursor-pointer"
              >
                <img src={partner.logo || "/placeholder.svg"} alt={partner.name} className="h-16 object-contain" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
