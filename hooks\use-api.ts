"use client"

import { useState, useEffect } from 'react'
import { apiClient, ApiResponse } from '@/lib/api-client'

export function useApi<T>(
  apiCall: () => Promise<ApiResponse<T>>,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let isMounted = true

    const fetchData = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const response = await apiCall()
        
        if (isMounted) {
          if (response.success && response.data) {
            setData(response.data)
          } else {
            setError(response.error || 'Failed to fetch data')
          }
        }
      } catch (err) {
        if (isMounted) {
          setError('Network error occurred')
        }
      } finally {
        if (isMounted) {
          setLoading(false)
        }
      }
    }

    fetchData()

    return () => {
      isMounted = false
    }
  }, dependencies)

  return { data, loading, error, refetch: () => setLoading(true) }
}

// Specific hooks for different data types
export function useServices(params?: {
  category_id?: number
  featured?: boolean
  limit?: number
}) {
  return useApi(
    () => apiClient.getServices(params || {}),
    [JSON.stringify(params)]
  )
}

export function useFeaturedServices(limit = 6) {
  return useApi(
    () => apiClient.getFeaturedServices(limit),
    [limit]
  )
}

export function useProjects(params?: {
  category_id?: number
  featured?: boolean
  limit?: number
  year?: number
}) {
  return useApi(
    () => apiClient.getProjects(params || {}),
    [JSON.stringify(params)]
  )
}

export function useFeaturedProjects(limit = 6) {
  return useApi(
    () => apiClient.getFeaturedProjects(limit),
    [limit]
  )
}

export function useArticles(params?: {
  category_id?: number
  featured?: boolean
  limit?: number
  popular?: boolean
}) {
  return useApi(
    () => apiClient.getArticles(params || {}),
    [JSON.stringify(params)]
  )
}

export function useFeaturedArticles(limit = 6) {
  return useApi(
    () => apiClient.getFeaturedArticles(limit),
    [limit]
  )
}

export function usePopularArticles(limit = 10) {
  return useApi(
    () => apiClient.getPopularArticles(limit),
    [limit]
  )
}

export function useVideos(params?: {
  category_id?: number
  featured?: boolean
  limit?: number
}) {
  return useApi(
    () => apiClient.getVideos(params || {}),
    [JSON.stringify(params)]
  )
}

export function useFeaturedVideos(limit = 6) {
  return useApi(
    () => apiClient.getFeaturedVideos(limit),
    [limit]
  )
}

export function useExperiences(params?: {
  category_id?: number
  featured?: boolean
  limit?: number
}) {
  return useApi(
    () => apiClient.getExperiences(params || {}),
    [JSON.stringify(params)]
  )
}

export function useFeaturedExperiences(limit = 6) {
  return useApi(
    () => apiClient.getFeaturedExperiences(limit),
    [limit]
  )
}

export function useCategories(type?: string) {
  return useApi(
    () => apiClient.getCategories(type),
    [type]
  )
}

export function useSearch(query: string, type?: string) {
  return useApi(
    () => apiClient.search(query, type as any),
    [query, type]
  )
}
