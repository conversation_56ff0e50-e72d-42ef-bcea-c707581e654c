import { Metadata } from "next"
import Header from "@/components/Header"
import Footer from "@/components/Footer"
import FloatingActionBar from "@/components/FloatingActionBar"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Play, Calendar, Eye, Clock } from "lucide-react"

export const metadata: Metadata = {
  title: "Video Công Trình Hoàn Thiện - Kiến <PERSON>",
  description: "Xem video các công trình nội thất đã hoàn thiện của Kiến Trúc <PERSON>",
}

// Temporary data - sẽ thay bằng API call
const categories = [
  { id: 'all', name: '<PERSON><PERSON><PERSON>', count: 28 },
  { id: 'biet-thu', name: '<PERSON><PERSON><PERSON><PERSON>h<PERSON>', count: 8 },
  { id: 'chung-cu', name: '<PERSON>', count: 10 },
  { id: 'nha-pho', name: '<PERSON><PERSON><PERSON>', count: 6 },
  { id: 'van-phong', name: '<PERSON><PERSON><PERSON>', count: 4 }
]

const videos = [
  {
    id: 1,
    title: "Biệt Thự Hiện Đại 500m² - Quận 7 | Hoàn Thiện Sau 6 Tháng",
    slug: "biet-thu-hien-dai-500m2-quan-7",
    category: "biet-thu",
    description: "Video tour chi tiết biệt thự hiện đại với thiết kế tối giản, sử dụng vật liệu cao cấp.",
    thumbnail: "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
    videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ", // Placeholder
    duration: "12:45",
    views: 15420,
    publishedDate: "2024-01-15",
    featured: true,
    area: "500m²",
    location: "Quận 7, TP.HCM",
    completionTime: "6 tháng"
  },
  {
    id: 2,
    title: "Chung Cư Landmark 81 - Thiết Kế Sang Trọng 120m²",
    slug: "chung-cu-landmark-81-120m2",
    category: "chung-cu",
    description: "Căn hộ cao cấp với view sông Sài Gòn, thiết kế nội thất luxury hiện đại.",
    thumbnail: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
    videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    duration: "8:30",
    views: 12350,
    publishedDate: "2024-01-10",
    featured: true,
    area: "120m²",
    location: "Landmark 81, TP.HCM",
    completionTime: "3 tháng"
  },
  {
    id: 3,
    title: "Nhà Phố Phong Cách Nhật Bản - Buôn Ma Thuột",
    slug: "nha-pho-phong-cach-nhat-ban-bmt",
    category: "nha-pho",
    description: "Nhà phố 3 tầng với thiết kế zen, sử dụng gỗ tự nhiên và ánh sáng tự nhiên.",
    thumbnail: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
    videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    duration: "10:15",
    views: 8900,
    publishedDate: "2024-01-05",
    featured: false,
    area: "180m²",
    location: "Buôn Ma Thuột",
    completionTime: "4 tháng"
  },
  {
    id: 4,
    title: "Văn Phòng Công Ty Công Nghệ - Không Gian Mở Hiện Đại",
    slug: "van-phong-cong-ty-cong-nghe",
    category: "van-phong",
    description: "Thiết kế văn phòng mở với concept tech startup, tối ưu hóa collaboration.",
    thumbnail: "https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
    videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    duration: "6:20",
    views: 5670,
    publishedDate: "2023-12-28",
    featured: false,
    area: "300m²",
    location: "Quận 1, TP.HCM",
    completionTime: "2 tháng"
  },
  {
    id: 5,
    title: "Chung Cư Vinhomes - Thiết Kế Tối Giản Scandinavian",
    slug: "chung-cu-vinhomes-scandinavian",
    category: "chung-cu",
    description: "Căn hộ 2 phòng ngủ với phong cách Scandinavian, tông màu sáng và gỗ tự nhiên.",
    thumbnail: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
    videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    duration: "9:45",
    views: 7890,
    publishedDate: "2023-12-20",
    featured: false,
    area: "85m²",
    location: "Vinhomes Central Park",
    completionTime: "2.5 tháng"
  },
  {
    id: 6,
    title: "Biệt Thự Vườn - Hòa Quyện Với Thiên Nhiên",
    slug: "biet-thu-vuon-hoa-quyen-thien-nhien",
    category: "biet-thu",
    description: "Biệt thự vườn với thiết kế xanh, tận dụng cảnh quan tự nhiên và ánh sáng.",
    thumbnail: "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
    videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    duration: "14:20",
    views: 11200,
    publishedDate: "2023-12-15",
    featured: false,
    area: "400m²",
    location: "Thủ Đức, TP.HCM",
    completionTime: "5 tháng"
  }
]

const featuredVideos = videos.filter(video => video.featured)
const regularVideos = videos.filter(video => !video.featured)

export default function VideoCongTrinhHoanThienPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Banner */}
      <section className="relative h-[400px] mt-[120px]">
        <div
          className="w-full h-full bg-cover bg-center"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1574375927938-d5a98e8ffe85?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=800')`
          }}
        >
          <div className="absolute inset-0 bg-black bg-opacity-50" />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-white">
              <h1 className="text-4xl md:text-6xl font-bold mb-4">VIDEO CÔNG TRÌNH HOÀN THIỆN</h1>
              <p className="text-xl md:text-2xl">Khám phá các dự án qua video chi tiết</p>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Filter */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap gap-4 justify-center">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={category.id === 'all' ? 'default' : 'outline'}
                className={`${
                  category.id === 'all' 
                    ? 'bg-[#3B82F6] hover:bg-[#2563EB] text-white' 
                    : 'border-[#3B82F6] text-[#3B82F6] hover:bg-[#3B82F6] hover:text-white'
                }`}
              >
                {category.name} ({category.count})
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Videos */}
      {featuredVideos.length > 0 && (
        <section className="py-12 bg-gray-50">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-[#1F2937] mb-8 text-center">VIDEO NỔI BẬT</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {featuredVideos.map((video) => (
                <div key={video.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                  <div className="relative h-64 group cursor-pointer">
                    <Image
                      src={video.thumbnail}
                      alt={video.title}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-30 group-hover:bg-opacity-50 transition-all duration-300" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <Play className="w-8 h-8 text-white ml-1" fill="white" />
                      </div>
                    </div>
                    <div className="absolute top-4 left-4">
                      <Badge className="bg-red-500 text-white">NỔI BẬT</Badge>
                    </div>
                    <div className="absolute bottom-4 right-4">
                      <Badge className="bg-black bg-opacity-70 text-white">
                        <Clock className="w-3 h-3 mr-1" />
                        {video.duration}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="p-6">
                    <div className="flex items-center gap-2 mb-3">
                      <Badge variant="outline" className="border-[#3B82F6] text-[#3B82F6]">
                        {categories.find(cat => cat.id === video.category)?.name}
                      </Badge>
                    </div>
                    
                    <h3 className="text-xl font-bold text-[#1F2937] mb-3 line-clamp-2">{video.title}</h3>
                    <p className="text-[#6B7280] mb-4 line-clamp-2">{video.description}</p>
                    
                    <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                      <div>
                        <span className="text-[#6B7280]">Diện tích:</span>
                        <span className="font-medium text-[#1F2937] ml-2">{video.area}</span>
                      </div>
                      <div>
                        <span className="text-[#6B7280]">Thời gian:</span>
                        <span className="font-medium text-[#1F2937] ml-2">{video.completionTime}</span>
                      </div>
                      <div className="col-span-2">
                        <span className="text-[#6B7280]">Địa điểm:</span>
                        <span className="font-medium text-[#1F2937] ml-2">{video.location}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm text-[#6B7280] mb-4">
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        <span>{new Date(video.publishedDate).toLocaleDateString('vi-VN')}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Eye className="w-4 h-4" />
                        <span>{video.views.toLocaleString()}</span>
                      </div>
                    </div>
                    
                    <Button className="w-full bg-[#3B82F6] hover:bg-[#2563EB] text-white">
                      Xem Video
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Regular Videos */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-[#1F2937] mb-12 text-center">TẤT CẢ VIDEO</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {regularVideos.map((video) => (
              <div key={video.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div className="relative h-48 group cursor-pointer">
                  <Image
                    src={video.thumbnail}
                    alt={video.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-30 group-hover:bg-opacity-50 transition-all duration-300" />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Play className="w-6 h-6 text-white ml-0.5" fill="white" />
                    </div>
                  </div>
                  <div className="absolute bottom-4 right-4">
                    <Badge className="bg-black bg-opacity-70 text-white text-xs">
                      <Clock className="w-3 h-3 mr-1" />
                      {video.duration}
                    </Badge>
                  </div>
                </div>
                
                <div className="p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <Badge variant="outline" className="border-[#3B82F6] text-[#3B82F6] text-xs">
                      {categories.find(cat => cat.id === video.category)?.name}
                    </Badge>
                  </div>
                  
                  <h3 className="text-lg font-bold text-[#1F2937] mb-3 line-clamp-2">{video.title}</h3>
                  <p className="text-[#6B7280] mb-4 line-clamp-2 text-sm">{video.description}</p>
                  
                  <div className="space-y-2 mb-4 text-sm">
                    <div className="flex justify-between">
                      <span className="text-[#6B7280]">Diện tích:</span>
                      <span className="font-medium text-[#1F2937]">{video.area}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-[#6B7280]">Địa điểm:</span>
                      <span className="font-medium text-[#1F2937]">{video.location}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-[#6B7280] mb-4">
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      <span>{new Date(video.publishedDate).toLocaleDateString('vi-VN')}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Eye className="w-4 h-4" />
                      <span>{video.views.toLocaleString()}</span>
                    </div>
                  </div>
                  
                  <Button className="w-full bg-[#3B82F6] hover:bg-[#2563EB] text-white">
                    Xem Video
                  </Button>
                </div>
              </div>
            ))}
          </div>
          
          {/* Pagination */}
          <div className="flex justify-center mt-12">
            <div className="flex gap-2">
              <Button variant="outline" disabled>Trước</Button>
              <Button className="bg-[#3B82F6] text-white">1</Button>
              <Button variant="outline">2</Button>
              <Button variant="outline">3</Button>
              <Button variant="outline">Sau</Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
      <FloatingActionBar />
    </div>
  )
}
