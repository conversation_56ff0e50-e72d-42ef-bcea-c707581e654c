import dotenv from 'dotenv'
import path from 'path'

// Load environment variables from .env.local
dotenv.config({ path: path.join(process.cwd(), '.env.local') })

console.log('Environment variables:')
console.log('DB_HOST:', process.env.DB_HOST)
console.log('DB_USER:', process.env.DB_USER)
console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? '***' : 'EMPTY')
console.log('DB_NAME:', process.env.DB_NAME)

import { initializeDatabase } from '../lib/database'
import { CategoryModel } from '../lib/models/category'
import { ServiceModel } from '../lib/models/service'
import { ProjectModel } from '../lib/models/project'
import { ArticleModel } from '../lib/models/article'
import { VideoModel } from '../lib/models/video'
import { ExperienceModel } from '../lib/models/experience'

async function seedDatabase() {
  console.log('🌱 Seeding database with sample data...')

  try {
    // Create categories
    const serviceCategories = [
      { name: '<PERSON><PERSON><PERSON><PERSON>', slug: 'thiet-ke-noi-that', description: 'Dịch vụ thiết kế nội thất chuyên nghiệp' },
      { name: 'Thi Công Trọn Gói', slug: 'thi-cong-tron-goi', description: 'Dịch vụ thi công nội thất từ A-Z' },
      { name: 'Bếp Nhật Bản', slug: 'bep-nhat-ban', description: 'Chuyên cung cấp bếp Nhật Bản cao cấp' },
      { name: 'Tư Vấn Thiết Kế', slug: 'tu-van-thiet-ke', description: 'Dịch vụ tư vấn thiết kế miễn phí' }
    ]

    const projectCategories = [
      { name: 'Nhà Phố', slug: 'nha-pho', description: 'Dự án thiết kế nội thất nhà phố' },
      { name: 'Chung Cư', slug: 'chung-cu', description: 'Dự án thiết kế nội thất chung cư' },
      { name: 'Biệt Thự', slug: 'biet-thu', description: 'Dự án thiết kế nội thất biệt thự' },
      { name: 'Văn Phòng', slug: 'van-phong', description: 'Dự án thiết kế nội thất văn phòng' },
      { name: 'Showroom', slug: 'showroom', description: 'Dự án thiết kế showroom' }
    ]

    const articleCategories = [
      { name: 'Xu Hướng', slug: 'xu-huong', description: 'Xu hướng thiết kế nội thất mới nhất' },
      { name: 'Mẹo Hay', slug: 'meo-hay', description: 'Mẹo hay trong thiết kế nội thất' },
      { name: 'Phong Thủy', slug: 'phong-thuy', description: 'Kiến thức về phong thủy trong nội thất' },
      { name: 'Vật Liệu', slug: 'vat-lieu', description: 'Thông tin về vật liệu nội thất' }
    ]

    const experienceCategories = [
      { name: 'Thiết Kế', slug: 'thiet-ke', description: 'Kinh nghiệm thiết kế' },
      { name: 'Thi Công', slug: 'thi-cong', description: 'Kinh nghiệm thi công' },
      { name: 'Quản Lý Dự Án', slug: 'quan-ly-du-an', description: 'Kinh nghiệm quản lý dự án' },
      { name: 'Tương Tác Khách Hàng', slug: 'tuong-tac-khach-hang', description: 'Kinh nghiệm tương tác khách hàng' }
    ]

    // Insert categories
    for (const category of [...serviceCategories, ...projectCategories, ...articleCategories, ...experienceCategories]) {
      await CategoryModel.create(category)
    }

    // Create sample services
    const sampleServices = [
      {
        title: 'Thiết Kế Nội Thất Hiện Đại',
        description: 'Dịch vụ thiết kế nội thất hiện đại, tối giản với phong cách châu Âu',
        content: 'Nội dung chi tiết về dịch vụ thiết kế nội thất hiện đại...',
        image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600',
        features: ['Thiết kế 2D, 3D chi tiết', 'Tư vấn phong thủy', 'Lựa chọn vật liệu cao cấp', 'Bảo hành thiết kế 2 năm'],
        price_range: '50-200 triệu',
        duration: '2-4 tuần',
        category_id: 1,
        featured: true
      },
      {
        title: 'Thi Công Trọn Gói Premium',
        description: 'Dịch vụ thi công nội thất trọn gói từ A-Z với chất lượng cao cấp',
        content: 'Nội dung chi tiết về dịch vụ thi công trọn gói...',
        image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600',
        features: ['Thi công hoàn thiện', 'Giám sát chất lượng', 'Bảo hành 5 năm', 'Hỗ trợ 24/7'],
        price_range: '200-800 triệu',
        duration: '2-6 tháng',
        category_id: 2,
        featured: true
      }
    ]

    for (const service of sampleServices) {
      await ServiceModel.create(service)
    }

    // Create sample projects
    const sampleProjects = [
      {
        title: 'Biệt Thự Hiện Đại Quận 7',
        description: 'Thiết kế nội thất biệt thự hiện đại với phong cách tối giản',
        content: 'Nội dung chi tiết về dự án biệt thự...',
        area: '350m²',
        location: 'TP. Hồ Chí Minh',
        completed_date: '2024-01-15',
        image: 'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600',
        images: [
          'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600',
          'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600'
        ],
        category_id: 7, // Biệt thự
        featured: true
      }
    ]

    for (const project of sampleProjects) {
      await ProjectModel.create(project)
    }

    console.log('✅ Database seeded successfully!')
  } catch (error) {
    console.error('❌ Error seeding database:', error)
  }
}

async function main() {
  try {
    console.log('🚀 Initializing database...')
    await initializeDatabase()
    await seedDatabase()
    console.log('🎉 Database initialization completed!')
    process.exit(0)
  } catch (error) {
    console.error('❌ Database initialization failed:', error)
    process.exit(1)
  }
}

main()
