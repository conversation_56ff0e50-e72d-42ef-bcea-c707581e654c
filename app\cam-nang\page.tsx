"use client"

import { useState, useEffect } from "react"
import Header from "@/components/Header"
import Footer from "@/components/Footer"
import FloatingActionBar from "@/components/FloatingActionBar"
import { Button } from "@/components/ui/button"
import { Loader2 } from "lucide-react"
import { apiClient } from "@/lib/api-client"
import ArticleCard from "@/components/shared/ArticleCard"
import CategoryFilter from "@/components/shared/CategoryFilter"
import SearchBox from "@/components/shared/SearchBox"
import Pagination from "@/components/shared/Pagination"

interface Article {
  id: number
  title: string
  slug: string
  excerpt?: string
  image?: string
  author?: string
  category_name?: string
  views?: number
  published_date?: string
  featured?: boolean
}

interface Category {
  id: number
  name: string
  count: number
}

export default function CamNangPage() {
  const [articles, setArticles] = useState<Article[]>([])
  const [featuredArticles, setFeaturedArticles] = useState<Article[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const itemsPerPage = 9

  useEffect(() => {
    fetchData()
  }, [selectedCategory, searchQuery, currentPage])

  const fetchData = async () => {
    setLoading(true)
    try {
      // Fetch categories
      const categoriesResponse = await apiClient.getCategories('articles')
      if (categoriesResponse.success) {
        setCategories(categoriesResponse.data || [])
      }

      // Fetch featured articles (only on first load)
      if (currentPage === 1 && !selectedCategory && !searchQuery) {
        const featuredResponse = await apiClient.getFeaturedArticles(3)
        if (featuredResponse.success) {
          setFeaturedArticles(featuredResponse.data || [])
        }
      }

      // Fetch articles
      const params: any = {
        limit: itemsPerPage,
        offset: (currentPage - 1) * itemsPerPage
      }
      
      if (selectedCategory) {
        params.category_id = selectedCategory
      }
      
      if (searchQuery) {
        params.search = searchQuery
      }

      const articlesResponse = await apiClient.getArticles(params)
      if (articlesResponse.success) {
        setArticles(articlesResponse.data || [])
        setTotalPages(articlesResponse.pagination?.totalPages || 1)
      }
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCategoryChange = (categoryId: number | null) => {
    setSelectedCategory(categoryId)
    setCurrentPage(1)
  }

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Banner */}
      <section className="relative h-[400px] mt-[120px]">
        <div
          className="w-full h-full bg-cover bg-center"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=800')`
          }}
        >
          <div className="absolute inset-0 bg-black bg-opacity-50" />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-white">
              <h1 className="text-5xl md:text-6xl font-bold mb-4">CẨM NANG</h1>
              <p className="text-xl md:text-2xl">Kiến thức và xu hướng thiết kế nội thất</p>
            </div>
          </div>
        </div>
      </section>

      {/* Search Section */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            <SearchBox
              placeholder="Tìm kiếm bài viết..."
              onSearch={handleSearch}
              className="w-full"
            />
          </div>
        </div>
      </section>

      {/* Categories Filter */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <CategoryFilter
            categories={categories}
            selectedCategory={selectedCategory}
            onCategoryChange={handleCategoryChange}
          />
        </div>
      </section>

      {/* Featured Articles */}
      {featuredArticles.length > 0 && !selectedCategory && !searchQuery && currentPage === 1 && (
        <section className="py-20 bg-gray-50">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Bài Viết Nổi Bật</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredArticles.map((article) => (
                <ArticleCard key={article.id} article={article} />
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Articles Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {articles.map((article) => (
                  <ArticleCard key={article.id} article={article} />
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="mt-12">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                  />
                </div>
              )}

              {articles.length === 0 && !loading && (
                <div className="text-center py-12">
                  <p className="text-gray-500 text-lg">Không tìm thấy bài viết nào.</p>
                </div>
              )}
            </>
          )}
        </div>
      </section>

      <Footer />
      <FloatingActionBar />
    </div>
  )
}
