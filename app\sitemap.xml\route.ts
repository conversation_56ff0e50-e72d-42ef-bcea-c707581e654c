import { generateSitemap, getSitemapRoutes } from '@/lib/sitemap';

export async function GET() {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://yourdomain.com';
  const routes = getSitemapRoutes();

  // Generate the XML sitemap
  const sitemap = generateSitemap(baseUrl, routes);

  // Return the sitemap XML with appropriate headers
  return new Response(sitemap, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600, s-maxage=3600',
    },
  });
}
