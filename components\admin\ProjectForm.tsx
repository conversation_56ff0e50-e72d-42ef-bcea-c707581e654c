"use client"

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Upload, X, Plus, Save, ArrowLeft } from 'lucide-react'
import { motion, AnimatePresence } from "framer-motion"
import { apiClient } from '@/lib/api-client'
import { uploadFile, validateFile } from '@/lib/upload'
import {
  fadeInUp,
  staggerContainer,
  staggerItem,
  buttonPress,
  getMotionProps
} from "@/lib/motion"

interface Category {
  id: number
  name: string
}

interface ProjectFormData {
  title: string
  slug: string
  description: string
  content: string
  area: string
  location: string
  year: number
  images: string[]
  category_id: number | null
  featured: boolean
  status: 'draft' | 'published' | 'archived'
}

interface ProjectFormProps {
  projectId?: number
  initialData?: Partial<ProjectFormData>
}

export default function ProjectForm({ projectId, initialData }: ProjectFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState('')
  const [categories, setCategories] = useState<Category[]>([])
  const [newImageUrl, setNewImageUrl] = useState('')

  const [formData, setFormData] = useState<ProjectFormData>({
    title: '',
    slug: '',
    description: '',
    content: '',
    area: '',
    location: '',
    year: new Date().getFullYear(),
    images: [],
    category_id: null,
    featured: false,
    status: 'draft',
    ...initialData
  })

  useEffect(() => {
    fetchCategories()
  }, [])

  const fetchCategories = async () => {
    try {
      const response = await apiClient.getCategories('projects')
      if (response.success) {
        setCategories(response.data || [])
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
    }
  }

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      slug: generateSlug(title)
    }))
  }

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    const validation = validateFile(file)
    if (!validation.valid) {
      setError(validation.error || 'Invalid file')
      return
    }

    setUploading(true)
    setError('')

    try {
      const result = await uploadFile(file, 'projects')
      if (result.success && result.data) {
        setFormData(prev => ({ 
          ...prev, 
          images: [...prev.images, result.data!.url] 
        }))
      } else {
        setError(result.error || 'Upload failed')
      }
    } catch (error) {
      setError('Upload failed')
    } finally {
      setUploading(false)
    }
  }

  const addImageUrl = () => {
    if (newImageUrl.trim()) {
      setFormData(prev => ({
        ...prev,
        images: [...prev.images, newImageUrl.trim()]
      }))
      setNewImageUrl('')
    }
  }

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const response = projectId
        ? await fetch(`/api/projects/${projectId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formData)
          })
        : await fetch('/api/projects', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formData)
          })

      const result = await response.json()

      if (result.success) {
        router.push('/admin/projects')
      } else {
        setError(result.error || 'Failed to save project')
      }
    } catch (error) {
      setError('Failed to save project')
    } finally {
      setLoading(false)
    }
  }

  return (
    <motion.div
      className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6"
      {...getMotionProps(fadeInUp)}
    >
      {/* Header with back button */}
      <motion.div
        className="mb-8"
        {...getMotionProps(fadeInUp)}
      >
        <div className="flex items-center space-x-4 mb-4">
          <motion.button
            onClick={() => window.history.back()}
            className="flex items-center space-x-2 text-muted-foreground hover:text-foreground transition-colors duration-200 p-2 rounded-lg hover:bg-accent/50"
            {...buttonPress}
            whileHover={{ x: -2 }}
          >
            <ArrowLeft className="h-5 w-5" />
            <span>Quay lại</span>
          </motion.button>
        </div>

        <motion.h1
          className="text-3xl font-bold text-foreground flex items-center space-x-3"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <motion.span
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ duration: 0.2 }}
          >
            {projectId ? '✏️' : '➕'}
          </motion.span>
          <span>{projectId ? 'Chỉnh Sửa Dự Án' : 'Tạo Dự Án Mới'}</span>
        </motion.h1>
        <motion.p
          className="mt-2 text-sm text-muted-foreground"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {projectId ? 'Cập nhật thông tin dự án thiết kế nội thất' : 'Thêm dự án thiết kế nội thất mới vào hệ thống'}
        </motion.p>
      </motion.div>

      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            <Alert variant="destructive" className="mb-6 border-destructive/20 bg-destructive/5">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </motion.div>
        )}
      </AnimatePresence>

      <motion.form
        onSubmit={handleSubmit}
        className="space-y-6"
        {...getMotionProps(staggerContainer)}
      >
        {/* Basic Information */}
        <motion.div {...getMotionProps(staggerItem)}>
          <Card className="border-border shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-accent/5 to-accent/10 border-b border-border/50">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
              >
                <CardTitle className="text-foreground flex items-center space-x-2">
                  <motion.span
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    📝
                  </motion.span>
                  <span>Thông Tin Cơ Bản</span>
                </CardTitle>
              </motion.div>
            </CardHeader>
            <CardContent className="space-y-6 p-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="space-y-2"
              >
                <Label htmlFor="title" className="text-sm font-medium text-foreground">
                  Tên dự án <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleTitleChange(e.target.value)}
                  required
                  className="transition-all duration-200 focus:ring-2 focus:ring-primary/20 focus:border-primary"
                  placeholder="Nhập tên dự án..."
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="space-y-2"
              >
                <Label htmlFor="slug" className="text-sm font-medium text-foreground">
                  Slug
                </Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                  className="transition-all duration-200 focus:ring-2 focus:ring-primary/20 focus:border-primary"
                  placeholder="URL slug tự động tạo..."
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="space-y-2"
              >
                <Label htmlFor="description" className="text-sm font-medium text-foreground">
                  Mô tả ngắn
                </Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="transition-all duration-200 focus:ring-2 focus:ring-primary/20 focus:border-primary resize-none"
                  placeholder="Mô tả ngắn gọn về dự án..."
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="space-y-2"
              >
                <Label htmlFor="content" className="text-sm font-medium text-foreground">
                  Nội dung chi tiết
                </Label>
                <Textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  rows={8}
                  className="transition-all duration-200 focus:ring-2 focus:ring-primary/20 focus:border-primary resize-none"
                  placeholder="Nội dung chi tiết về dự án..."
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="grid grid-cols-1 md:grid-cols-3 gap-4"
              >
                <div className="space-y-2">
                  <Label htmlFor="area" className="text-sm font-medium text-foreground">
                    Diện tích
                  </Label>
                  <Input
                    id="area"
                    value={formData.area}
                    onChange={(e) => setFormData(prev => ({ ...prev, area: e.target.value }))}
                    className="transition-all duration-200 focus:ring-2 focus:ring-primary/20 focus:border-primary"
                    placeholder="VD: 120m²"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location" className="text-sm font-medium text-foreground">
                    Địa điểm
                  </Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                    className="transition-all duration-200 focus:ring-2 focus:ring-primary/20 focus:border-primary"
                    placeholder="VD: TP. Hồ Chí Minh"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="year" className="text-sm font-medium text-foreground">
                    Năm hoàn thành
                  </Label>
                  <Input
                    id="year"
                    type="number"
                    value={formData.year}
                    onChange={(e) => setFormData(prev => ({ ...prev, year: parseInt(e.target.value) || new Date().getFullYear() }))}
                    className="transition-all duration-200 focus:ring-2 focus:ring-primary/20 focus:border-primary"
                    min="2000"
                    max="2030"
                  />
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="space-y-2"
              >
                <Label htmlFor="category" className="text-sm font-medium text-foreground">
                  Danh mục
                </Label>
                <Select
                  value={formData.category_id?.toString() || ''}
                  onValueChange={(value) => setFormData(prev => ({ 
                    ...prev, 
                    category_id: value ? parseInt(value) : null 
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn danh mục..." />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Images Section */}
        <motion.div {...getMotionProps(staggerItem)}>
          <Card className="border-border shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-accent/5 to-accent/10 border-b border-border/50">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
              >
                <CardTitle className="text-foreground flex items-center space-x-2">
                  <motion.span
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    🖼️
                  </motion.span>
                  <span>Hình Ảnh Dự Án</span>
                </CardTitle>
              </motion.div>
            </CardHeader>
            <CardContent className="space-y-4 p-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="space-y-2"
              >
                <Label htmlFor="image" className="text-sm font-medium text-foreground">
                  Upload hình ảnh
                </Label>
                <Input
                  id="image"
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  disabled={uploading}
                  className="transition-all duration-200 focus:ring-2 focus:ring-primary/20 focus:border-primary"
                />
                {uploading && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="flex items-center mt-2 text-sm text-muted-foreground"
                  >
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Đang tải lên...
                  </motion.div>
                )}
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="flex space-x-2"
              >
                <Input
                  value={newImageUrl}
                  onChange={(e) => setNewImageUrl(e.target.value)}
                  placeholder="Hoặc nhập URL hình ảnh..."
                  className="flex-1 transition-all duration-200 focus:ring-2 focus:ring-primary/20 focus:border-primary"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addImageUrl())}
                />
                <Button
                  type="button"
                  onClick={addImageUrl}
                  className="px-4 py-2 bg-primary hover:bg-primary/90 transition-all duration-200"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="grid grid-cols-2 md:grid-cols-4 gap-4"
              >
                {formData.images.map((image, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    className="relative group"
                  >
                    <img
                      src={image}
                      alt={`Project image ${index + 1}`}
                      className="w-full h-24 object-cover rounded-lg border border-border shadow-sm"
                    />
                    <motion.button
                      type="button"
                      onClick={() => removeImage(index)}
                      className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground rounded-full p-1 shadow-md hover:bg-destructive/90 transition-colors duration-200 opacity-0 group-hover:opacity-100"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <X className="h-4 w-4" />
                    </motion.button>
                  </motion.div>
                ))}
                {formData.images.length === 0 && (
                  <p className="text-sm text-muted-foreground text-center py-4 col-span-full">
                    Chưa có hình ảnh nào. Thêm hình ảnh để showcase dự án.
                  </p>
                )}
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Settings Section */}
        <motion.div {...getMotionProps(staggerItem)}>
          <Card className="border-border shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-accent/5 to-accent/10 border-b border-border/50">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
              >
                <CardTitle className="text-foreground flex items-center space-x-2">
                  <motion.span
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    ⚙️
                  </motion.span>
                  <span>Cài Đặt</span>
                </CardTitle>
              </motion.div>
            </CardHeader>
            <CardContent className="space-y-6 p-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="flex items-center justify-between p-4 bg-accent/5 rounded-lg border border-border/30"
              >
                <div className="space-y-1">
                  <Label htmlFor="featured" className="text-sm font-medium text-foreground">
                    Dự án nổi bật
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Hiển thị dự án này ở vị trí nổi bật trên trang chủ
                  </p>
                </div>
                <Switch
                  id="featured"
                  checked={formData.featured}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, featured: checked }))}
                />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="space-y-2"
              >
                <Label htmlFor="status" className="text-sm font-medium text-foreground">
                  Trạng thái
                </Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: 'draft' | 'published' | 'archived') => 
                    setFormData(prev => ({ ...prev, status: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                        <span>Bản nháp</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="published">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                        <span>Đã xuất bản</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="archived">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-gray-500"></div>
                        <span>Đã lưu trữ</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          className="flex justify-end space-x-4 pt-6 border-t border-border"
          {...getMotionProps(staggerItem)}
        >
          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              className="px-6 py-2 transition-all duration-200 hover:bg-accent/50"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Hủy bỏ
            </Button>
          </motion.div>

          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-primary hover:bg-primary/90 transition-all duration-200 shadow-md hover:shadow-lg"
            >
              {loading ? (
                <motion.div
                  className="flex items-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                >
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Đang lưu...
                </motion.div>
              ) : (
                <motion.div
                  className="flex items-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                >
                  <Save className="mr-2 h-4 w-4" />
                  {projectId ? 'Cập nhật dự án' : 'Tạo dự án'}
                </motion.div>
              )}
            </Button>
          </motion.div>
        </motion.div>
      </motion.form>
    </motion.div>
  )
}
