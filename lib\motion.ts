/**
 * Motion configuration and utilities for enhanced admin panel animations
 * Respects user motion preferences and provides consistent animation settings
 */

import { Variants, Transition } from "framer-motion"

// Check if user prefers reduced motion
export const prefersReducedMotion = () => {
  if (typeof window === 'undefined') return false
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches
}

// Base transition configurations
export const transitions = {
  fast: {
    duration: 0.15,
    ease: [0.16, 1, 0.3, 1] as const,
  },
  normal: {
    duration: 0.25,
    ease: [0.16, 1, 0.3, 1] as const,
  },
  slow: {
    duration: 0.35,
    ease: [0.16, 1, 0.3, 1] as const,
  },
  spring: {
    type: "spring" as const,
    stiffness: 300,
    damping: 30,
  },
  bouncy: {
    type: "spring" as const,
    stiffness: 400,
    damping: 25,
  },
} as const

// Common animation variants
export const fadeInUp: Variants = {
  initial: {
    opacity: 0,
    y: 20,
  },
  animate: {
    opacity: 1,
    y: 0,
    transition: transitions.normal,
  },
  exit: {
    opacity: 0,
    y: -10,
    transition: transitions.fast,
  },
}

export const fadeInRight: Variants = {
  initial: {
    opacity: 0,
    x: -20,
  },
  animate: {
    opacity: 1,
    x: 0,
    transition: transitions.normal,
  },
  exit: {
    opacity: 0,
    x: 10,
    transition: transitions.fast,
  },
}

export const scaleIn: Variants = {
  initial: {
    opacity: 0,
    scale: 0.9,
  },
  animate: {
    opacity: 1,
    scale: 1,
    transition: transitions.spring,
  },
  exit: {
    opacity: 0,
    scale: 0.95,
    transition: transitions.fast,
  },
}

export const slideInFromBottom: Variants = {
  initial: {
    opacity: 0,
    y: 30,
  },
  animate: {
    opacity: 1,
    y: 0,
    transition: transitions.normal,
  },
  exit: {
    opacity: 0,
    y: 20,
    transition: transitions.fast,
  },
}

// Stagger animation for lists
export const staggerContainer: Variants = {
  initial: {},
  animate: {
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
  exit: {
    transition: {
      staggerChildren: 0.05,
      staggerDirection: -1,
    },
  },
}

export const staggerItem: Variants = {
  initial: {
    opacity: 0,
    y: 20,
  },
  animate: {
    opacity: 1,
    y: 0,
    transition: transitions.normal,
  },
  exit: {
    opacity: 0,
    y: -10,
    transition: transitions.fast,
  },
}

// Hover animations
export const hoverLift = {
  whileHover: {
    y: -2,
    transition: transitions.fast,
  },
  whileTap: {
    scale: 0.98,
    transition: transitions.fast,
  },
}

export const hoverScale = {
  whileHover: {
    scale: 1.02,
    transition: transitions.fast,
  },
  whileTap: {
    scale: 0.98,
    transition: transitions.fast,
  },
}

// Loading animations
export const loadingSpinner: Variants = {
  animate: {
    rotate: 360,
    transition: {
      duration: 1,
      repeat: Infinity,
      ease: "linear",
    },
  },
}

export const loadingPulse: Variants = {
  animate: {
    opacity: [1, 0.5, 1],
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: "easeInOut",
    },
  },
}

// Modal/Dialog animations
export const modalBackdrop: Variants = {
  initial: {
    opacity: 0,
  },
  animate: {
    opacity: 1,
    transition: transitions.normal,
  },
  exit: {
    opacity: 0,
    transition: transitions.fast,
  },
}

export const modalContent: Variants = {
  initial: {
    opacity: 0,
    scale: 0.9,
    y: 20,
  },
  animate: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: transitions.spring,
  },
  exit: {
    opacity: 0,
    scale: 0.95,
    y: 10,
    transition: transitions.fast,
  },
}

// Sidebar animations
export const sidebarSlide: Variants = {
  initial: {
    x: -280,
  },
  animate: {
    x: 0,
    transition: transitions.normal,
  },
  exit: {
    x: -280,
    transition: transitions.normal,
  },
}

// Navigation item animations
export const navItemHover = {
  whileHover: {
    x: 4,
    transition: transitions.fast,
  },
}

// Card animations
export const cardHover = {
  whileHover: {
    y: -4,
    boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
    transition: transitions.normal,
  },
}

// Button animations
export const buttonPress = {
  whileTap: {
    scale: 0.95,
    transition: transitions.fast,
  },
}

// Utility function to get motion props based on user preferences
export const getMotionProps = (variants: Variants, options?: {
  initial?: string
  animate?: string
  exit?: string
}) => {
  if (prefersReducedMotion()) {
    return {
      initial: false,
      animate: false,
      exit: false,
    }
  }

  return {
    variants,
    initial: options?.initial || "initial",
    animate: options?.animate || "animate",
    exit: options?.exit || "exit",
  }
}

// Utility function for hover animations
export const getHoverProps = (hoverVariant: any) => {
  if (prefersReducedMotion()) {
    return {}
  }
  return hoverVariant
}
