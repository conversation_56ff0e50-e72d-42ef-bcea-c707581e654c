import { Phone } from "lucide-react"

const ecosystemItems = [
  {
    id: 1,
    logo: "https://images.unsplash.com/photo-1599305445671-ac291c95aaa9?ixlib=rb-4.0.3&auto=format&fit=crop&w=120&h=80",
    title: "BẾP NHÀ BẠN",
    description: "<PERSON><PERSON><PERSON><PERSON> cung cấp bếp <PERSON><PERSON><PERSON><PERSON> cao cấp, thiết bị nhà bếp hiện đại với chất lượng tuyệt vời.",
    phone: "0123 456 789",
    fanpage: "facebook.com/bepnhaban",
  },
  {
    id: 2,
    logo: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=120&h=80",
    title: "KIẾN TRÚC SƯ THÁI VŨ",
    description: "Dịch vụ thiết kế và thi công nội thất chuyên nghi<PERSON>, tạ<PERSON> nên không gian sống hoàn hảo.",
    phone: "0123 456 790",
    fanpage: "facebook.com/ktsthaivuarchitect",
  },
  {
    id: 3,
    logo: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=120&h=80",
    title: "NỘI THẤT CAO CẤP",
    description: "Showroom nội thất cao cấp với đa dạng sản phẩm từ các thương hiệu nổi tiếng thế giới.",
    phone: "0123 456 791",
    fanpage: "facebook.com/noithatcaocap",
  },
]

export default function Ecosystem() {
  return (
    <section className="py-20 bg-[#F7F7F7]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-[#1F2937] mb-6 tracking-wider">HỆ SINH THÁI</h2>
          <p className="text-xl text-[#6B7280] max-w-3xl mx-auto">
            Hệ thống các thương hiệu và dịch vụ đa dạng phục vụ mọi nhu cầu nội thất của bạn
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {ecosystemItems.map((item) => (
            <div
              key={item.id}
              className="bg-white rounded-lg shadow-lg p-8 text-center group hover:shadow-xl transition-all duration-300 hover:transform hover:scale-105"
            >
              <div className="mb-6">
                <img src={item.logo || "/placeholder.svg"} alt={item.title} className="mx-auto h-20 object-contain" />
              </div>
              <h3 className="text-2xl font-bold text-[#1F2937] mb-4">{item.title}</h3>
              <p className="text-[#6B7280] leading-relaxed mb-6">{item.description}</p>
              <div className="space-y-3">
                <div className="flex items-center justify-center gap-2 text-[#3B82F6]">
                  <Phone className="w-4 h-4" />
                  <span className="font-semibold">{item.phone}</span>
                </div>
                <div className="text-sm text-[#6B7280]">Fanpage: {item.fanpage}</div>
              </div>
              <button className="mt-6 bg-[#3B82F6] hover:bg-[#2563EB] text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                Xem thêm
              </button>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
