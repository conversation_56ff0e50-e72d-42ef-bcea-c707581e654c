"use client"

import { useState, useEffect } from "react"
import { ArrowUp, Phone, MessageCircle, MapPin } from "lucide-react"

export default function FloatingActionBar() {
  const [showScrollTop, setShowScrollTop] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  return (
    <div className="fixed bottom-6 right-6 flex flex-col gap-3 z-50">
      {/* Scroll to Top */}
      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="bg-[#3B82F6] hover:bg-[#2563EB] text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110"
        >
          <ArrowUp className="w-5 h-5" />
        </button>
      )}

      {/* Phone */}
      <a
        href="tel:0123456789"
        className="bg-green-500 hover:bg-green-600 text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110"
      >
        <Phone className="w-5 h-5" />
      </a>

      {/* Messenger */}
      <a
        href="#"
        className="bg-[#0084FF] hover:bg-[#0066CC] text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110"
      >
        <MessageCircle className="w-5 h-5" />
      </a>

      {/* Zalo */}
      <a
        href="#"
        className="bg-[#0068FF] hover:bg-[#0052CC] text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110"
      >
        <span className="text-sm font-bold">Z</span>
      </a>

      {/* Google Maps */}
      <a
        href="https://maps.google.com"
        target="_blank"
        rel="noopener noreferrer"
        className="bg-red-500 hover:bg-red-600 text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110"
      >
        <MapPin className="w-5 h-5" />
      </a>
    </div>
  )
}
