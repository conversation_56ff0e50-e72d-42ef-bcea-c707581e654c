"use client"
import { Play, Youtube } from "lucide-react"

const youtubeVideos = [
  {
    id: 1,
    title: "Thi<PERSON><PERSON> kế bếp hiện đại 2024",
    thumbnail:
      "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200",
    duration: "10:25",
  },
  {
    id: 2,
    title: "<PERSON><PERSON> công nội thất villa cao cấp",
    thumbnail:
      "https://images.unsplash.com/photo-1600210492486-724fe5c67fb0?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200",
    duration: "15:30",
  },
  {
    id: 3,
    title: "Review bếp Nhật Bản nhập khẩu",
    thumbnail:
      "https://images.unsplash.com/photo-1556909085-4c0c4b0c6f8e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200",
    duration: "8:45",
  },
  {
    id: 4,
    title: "<PERSON> hướng nội thất 2024",
    thumbnail:
      "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200",
    duration: "12:15",
  },
]

const tiktokVideos = [
  {
    id: 1,
    title: "Tips thiết kế phòng nhỏ",
    thumbnail:
      "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200",
    views: "1.2M",
  },
  {
    id: 2,
    title: "Bếp Nhật siêu tiện lợi",
    thumbnail:
      "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200",
    views: "850K",
  },
  {
    id: 3,
    title: "Phòng khách sang chảnh",
    thumbnail:
      "https://images.unsplash.com/photo-1555041469-a586c61ea9bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200",
    views: "2.1M",
  },
  {
    id: 4,
    title: "Nội thất thông minh",
    thumbnail:
      "https://images.unsplash.com/photo-1631049307264-da0ec9d70304?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200",
    views: "1.8M",
  },
]

export default function MediaChannels() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-[#1F2937] mb-6 tracking-wider">
            KÊNH TRUYỀN THÔNG TRỰC TUYẾN
          </h2>
          <p className="text-xl text-[#6B7280] max-w-3xl mx-auto">
            Theo dõi chúng tôi trên các nền tảng để cập nhật những nội dung mới nhất
          </p>
        </div>

        {/* YouTube Section */}
        <div className="mb-16">
          <div className="flex items-center justify-center gap-3 mb-8">
            <Youtube className="w-8 h-8 text-[#FF0000]" />
            <h3 className="text-3xl font-bold text-[#1F2937]">YOUTUBE</h3>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {youtubeVideos.map((video) => (
              <div
                key={video.id}
                className="group cursor-pointer transform transition-all duration-300 hover:scale-105"
              >
                <div className="relative overflow-hidden rounded-lg shadow-lg">
                  <div
                    className="relative h-48 bg-cover bg-center"
                    style={{ backgroundImage: `url(${video.thumbnail})` }}
                  >
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300" />
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="bg-[#FF0000] text-white p-4 rounded-full">
                        <Play className="w-6 h-6" />
                      </div>
                    </div>
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-80 text-white px-2 py-1 rounded text-sm">
                      {video.duration}
                    </div>
                  </div>
                  <div className="p-4 bg-white">
                    <h4 className="font-semibold text-[#1F2937] group-hover:text-[#FF0000] transition-colors">
                      {video.title}
                    </h4>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* TikTok Section */}
        <div>
          <div className="flex items-center justify-center gap-3 mb-8">
            <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">TT</span>
            </div>
            <h3 className="text-3xl font-bold text-[#1F2937]">TIKTOK</h3>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {tiktokVideos.map((video) => (
              <div
                key={video.id}
                className="group cursor-pointer transform transition-all duration-300 hover:scale-105"
              >
                <div className="relative overflow-hidden rounded-lg shadow-lg">
                  <div
                    className="relative h-48 bg-cover bg-center"
                    style={{ backgroundImage: `url(${video.thumbnail})` }}
                  >
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300" />
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="bg-black text-white p-4 rounded-full">
                        <Play className="w-6 h-6" />
                      </div>
                    </div>
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-80 text-white px-2 py-1 rounded text-sm">
                      {video.views}
                    </div>
                  </div>
                  <div className="p-4 bg-white">
                    <h4 className="font-semibold text-[#1F2937] group-hover:text-black transition-colors">
                      {video.title}
                    </h4>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
