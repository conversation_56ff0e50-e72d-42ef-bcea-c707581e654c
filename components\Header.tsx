"use client"

import { useState, useEffect } from "react"
import { Search, Phone, Facebook, Youtube, Home, Info, Wrench, FolderOpen, BookOpen, Mail, MapPin, Instagram, Video, Share, Users, Menu, X } from "lucide-react"
import { Input } from "@/components/ui/input"

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [currentPath, setCurrentPath] = useState("")
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }
    window.addEventListener("scroll", handleScroll)

    // Set current path
    setCurrentPath(window.location.pathname)

    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen)
    // Prevent scrolling when menu is open
    if (!mobileMenuOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'auto'
    }
  }

  return (
    <header className="fixed top-0 left-0 right-0 z-50">
      {/* Top Bar */}
      <div className="bg-black text-white py-2 px-4">
        <div className="container mx-auto flex justify-between items-center text-sm">
          <div className="flex items-center gap-6">
            <span className="flex items-center gap-2">
              <Phone className="w-4 h-4" />
              0935754586
            </span>
            <span className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              104 Ngô Quyền, P. Tân Lợi, TP. BMT
            </span>
          </div>
          <div className="flex items-center gap-3">
            <Facebook className="w-4 h-4 cursor-pointer hover:text-[#3B82F6]" />
            <Youtube className="w-4 h-4 cursor-pointer hover:text-[#FF0000]" />
            <Instagram className="w-4 h-4 cursor-pointer hover:text-[#E4405F]" />
          </div>
        </div>
      </div>

      {/* Main Navigation */}
      <nav className={`bg-white transition-all duration-300 ${isScrolled ? "shadow-lg" : ""}`}>
        <div className="mx-auto px-4 py-4">
          <div className="container mx-auto flex items-center justify-between">
            {/* Logo */}
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-black text-white rounded flex items-center justify-center">
                <span className="text-xl font-bold">B</span>
                <span className="text-sm">+</span>
              </div>
              <div>
                <div className="text-lg font-bold text-[#1F2937]">Kiến Trúc Sư</div>
                <div className="text-lg font-bold text-[#1F2937]">Thái Vũ</div>
              </div>
            </div>

            {/* Center: Search - Hide on mobile */}
            <div className="hidden md:block flex-1 mx-8">
              <div className="relative">
                <Input type="text" placeholder="Tìm kiếm..." className="w-full pr-10 border-[#E5E7EB] rounded-full" />
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#6B7280]" />
              </div>
            </div>

            {/* Mobile menu button */}
            <button
              className="md:hidden ml-auto mr-4 p-2 rounded-md hover:bg-gray-100"
              onClick={toggleMobileMenu}
              aria-label="Toggle menu"
            >
              {mobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>

            {/* Right: Phone Number */}
            <div className="hidden md:flex items-center gap-2 bg-[#3B82F6] text-white px-6 py-3 rounded-full hover:bg-[#2563EB] transition-colors cursor-pointer">
              <Phone className="w-4 h-4" />
              <span className="font-semibold text-lg">0935754586</span>
            </div>
          </div>

          {/* Desktop Menu with Icons */}
          <div className="hidden md:flex items-center justify-center space-x-1 mt-6 pt-3 pb-3 py-2">
            <a href="/" className={`flex items-center gap-1 font-medium py-2 text-sm rounded transition-colors ${
              currentPath === "/" ? "text-[#3B82F6] bg-white" : "text-[#1F2937] hover:text-[#3B82F6]"
            }`}>
              <Home className="w-5 h-5" />
              TRANG CHỦ
            </a>
            <span className="text-gray-400">|</span>
            <a href="/dich-vu" className={`flex items-center gap-1 font-medium px-3 py-2 text-sm rounded transition-colors ${
              currentPath === "/dich-vu" ? "text-[#3B82F6] bg-white" : "text-[#1F2937] hover:text-[#3B82F6]"
            }`}>
              <Wrench className="w-5 h-5" />
              DỊCH VỤ
            </a>
            <span className="text-gray-400">|</span>
            <a href="/du-an-da-hoan-thien" className={`flex items-center gap-1 font-medium px-3 py-2 text-sm rounded transition-colors ${
              currentPath === "/du-an-da-hoan-thien" ? "text-[#3B82F6] bg-white" : "text-[#1F2937] hover:text-[#3B82F6]"
            }`}>
              <FolderOpen className="w-5 h-5" />
              DỰ ÁN ĐÃ HOÀN THIỆN
            </a>
            <span className="text-gray-400">|</span>
            <a href="/cam-nang" className={`flex items-center gap-1 font-medium px-3 py-2 text-sm rounded transition-colors ${
              currentPath === "/cam-nang" ? "text-[#3B82F6] bg-white" : "text-[#1F2937] hover:text-[#3B82F6]"
            }`}>
              <BookOpen className="w-5 h-5" />
              CẨM NANG
            </a>
            <span className="text-gray-400">|</span>
            <a href="/chia-se-kinh-nghiem" className={`flex items-center gap-1 font-medium px-3 py-2 text-sm rounded transition-colors ${
              currentPath === "/chia-se-kinh-nghiem" ? "text-[#3B82F6] bg-white" : "text-[#1F2937] hover:text-[#3B82F6]"
            }`}>
              <Share className="w-5 h-5" />
              CHIA SẺ KINH NGHIỆM
            </a>
            <span className="text-gray-400">|</span>
            <a href="/video-cong-trinh-hoan-thien" className={`flex items-center gap-1 font-medium px-3 py-2 text-sm rounded transition-colors ${
              currentPath === "/video-cong-trinh-hoan-thien" ? "text-[#3B82F6] bg-white" : "text-[#1F2937] hover:text-[#3B82F6]"
            }`}>
              <Video className="w-5 h-5" />
              VIDEO CÔNG TRÌNH HOÀN THIỆN
            </a>
            <span className="text-gray-400">|</span>
            <a href="/gioi-thieu" className={`flex items-center gap-1 font-medium px-3 py-2 text-sm rounded transition-colors ${
              currentPath === "/gioi-thieu" ? "text-[#3B82F6] bg-white" : "text-[#1F2937] hover:text-[#3B82F6]"
            }`}>
              <Users className="w-5 h-5" />
              GIỚI THIỆU
            </a>
            <span className="text-gray-400">|</span>
            <a href="/gui-yeu-cau" className={`flex items-center gap-1 font-medium px-3 py-2 text-sm rounded transition-colors ${
              currentPath === "/gui-yeu-cau" ? "text-[#3B82F6] bg-white" : "text-[#1F2937] hover:text-[#3B82F6]"
            }`}>
              <Mail className="w-5 h-5" />
              GỬI YÊU CẦU
            </a>
          </div>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="fixed inset-0 z-50 bg-white pt-24 pb-6 px-6 overflow-y-auto md:hidden">
            <div className="flex flex-col space-y-4">
              <a 
                href="/" 
                className={`flex items-center gap-3 py-3 px-4 rounded-md ${currentPath === "/" ? "bg-blue-50 text-blue-600" : "text-gray-700"}`}
                onClick={toggleMobileMenu}
              >
                <Home className="w-5 h-5" />
                <span className="font-medium">Trang Chủ</span>
              </a>

              <a 
                href="/dich-vu" 
                className={`flex items-center gap-3 py-3 px-4 rounded-md ${currentPath === "/dich-vu" ? "bg-blue-50 text-blue-600" : "text-gray-700"}`}
                onClick={toggleMobileMenu}
              >
                <Wrench className="w-5 h-5" />
                <span className="font-medium">Dịch Vụ</span>
              </a>

              <a 
                href="/du-an-da-hoan-thien" 
                className={`flex items-center gap-3 py-3 px-4 rounded-md ${currentPath === "/du-an-da-hoan-thien" ? "bg-blue-50 text-blue-600" : "text-gray-700"}`}
                onClick={toggleMobileMenu}
              >
                <FolderOpen className="w-5 h-5" />
                <span className="font-medium">Dự Án Đã Hoàn Thiện</span>
              </a>

              <a 
                href="/cam-nang" 
                className={`flex items-center gap-3 py-3 px-4 rounded-md ${currentPath === "/cam-nang" ? "bg-blue-50 text-blue-600" : "text-gray-700"}`}
                onClick={toggleMobileMenu}
              >
                <BookOpen className="w-5 h-5" />
                <span className="font-medium">Cẩm Nang</span>
              </a>

              <a 
                href="/chia-se-kinh-nghiem" 
                className={`flex items-center gap-3 py-3 px-4 rounded-md ${currentPath === "/chia-se-kinh-nghiem" ? "bg-blue-50 text-blue-600" : "text-gray-700"}`}
                onClick={toggleMobileMenu}
              >
                <Share className="w-5 h-5" />
                <span className="font-medium">Chia Sẻ Kinh Nghiệm</span>
              </a>

              <a 
                href="/video-cong-trinh-hoan-thien" 
                className={`flex items-center gap-3 py-3 px-4 rounded-md ${currentPath === "/video-cong-trinh-hoan-thien" ? "bg-blue-50 text-blue-600" : "text-gray-700"}`}
                onClick={toggleMobileMenu}
              >
                <Video className="w-5 h-5" />
                <span className="font-medium">Video Công Trình Hoàn Thiện</span>
              </a>

              <a 
                href="/gioi-thieu" 
                className={`flex items-center gap-3 py-3 px-4 rounded-md ${currentPath === "/gioi-thieu" ? "bg-blue-50 text-blue-600" : "text-gray-700"}`}
                onClick={toggleMobileMenu}
              >
                <Users className="w-5 h-5" />
                <span className="font-medium">Giới Thiệu</span>
              </a>

              <a 
                href="/gui-yeu-cau" 
                className={`flex items-center gap-3 py-3 px-4 rounded-md ${currentPath === "/gui-yeu-cau" ? "bg-blue-50 text-blue-600" : "text-gray-700"}`}
                onClick={toggleMobileMenu}
              >
                <Mail className="w-5 h-5" />
                <span className="font-medium">Gửi Yêu Cầu</span>
              </a>

              {/* Search in mobile menu */}
              <div className="mt-4 relative">
                <Input type="text" placeholder="Tìm kiếm..." className="w-full pr-10 border-gray-300 rounded-md" />
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-500" />
              </div>

              {/* Contact in mobile menu */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <a href="tel:0935754586" className="flex items-center gap-3 py-3 px-4 bg-blue-600 text-white rounded-md">
                  <Phone className="w-5 h-5" />
                  <span className="font-medium">0935754586</span>
                </a>
              </div>
            </div>
          </div>
        )}
      </nav>
    </header>
  )
}
