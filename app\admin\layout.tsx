"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useSession, signOut } from "next-auth/react"
import { SessionProvider } from "next-auth/react"
import { motion, AnimatePresence } from "framer-motion"
import {
  LayoutDashboard,
  Briefcase,
  FolderOpen,
  FileText,
  Video,
  Users,
  Tags,
  Menu,
  X,
  Settings,
  LogOut,
  ChevronRight,
  Bell,
  Search
} from "lucide-react"
import {
  sidebarSlide,
  navItemHover,
  fadeInRight,
  buttonPress,
  getMotionProps
} from "@/lib/motion"

const navigation = [
  { name: 'Dashboard', href: '/admin/dashboard', icon: LayoutDashboard, badge: null },
  { name: '<PERSON><PERSON><PERSON>', href: '/admin/services', icon: Briefcase, badge: null },
  { name: 'Dự Án', href: '/admin/projects', icon: FolderOpen, badge: null },
  { name: '<PERSON><PERSON><PERSON>', href: '/admin/articles', icon: FileText, badge: '<PERSON>ớ<PERSON>' },
  { name: 'Video', href: '/admin/videos', icon: Video, badge: null },
  { name: '<PERSON><PERSON>', href: '/admin/experiences', icon: Users, badge: null },
  { name: 'Danh Mục', href: '/admin/categories', icon: Tags, badge: null },
]

function AdminLayoutContent({
  children,
}: {
  children: React.ReactNode
}) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const pathname = usePathname()
  const { data: session, status } = useSession()

  const handleLogout = () => {
    signOut({ callbackUrl: '/admin/login' })
  }

  // Allow access to login page without authentication
  if (pathname === '/admin/login') {
    return <>{children}</>
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-4">You need to be logged in to access this page.</p>
          <Link href="/admin/login" className="text-blue-600 hover:text-blue-800">
            Go to Login
          </Link>
        </div>
      </div>
    )
  }

  return (
    <motion.div
      className="min-h-screen bg-background"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* Mobile sidebar */}
      <AnimatePresence>
        {sidebarOpen && (
          <div className="fixed inset-0 z-50 lg:hidden">
            <motion.div
              className="fixed inset-0 bg-black/50"
              onClick={() => setSidebarOpen(false)}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
            />
            <motion.div
              className="fixed inset-y-0 left-0 flex w-64 flex-col bg-sidebar shadow-xl border-r border-sidebar-border"
              {...getMotionProps(sidebarSlide)}
            >
              <div className="flex h-16 items-center justify-between px-4 border-b border-sidebar-border">
                <motion.h1
                  className="text-xl font-bold text-sidebar-foreground flex items-center"
                  {...getMotionProps(fadeInRight)}
                >
                  <motion.span
                    className="mr-2"
                    whileHover={{ scale: 1.1, rotate: 10 }}
                    transition={{ duration: 0.2 }}
                  >
                    🏠
                  </motion.span>
                  Interior Admin
                </motion.h1>
                <motion.button
                  onClick={() => setSidebarOpen(false)}
                  className="text-sidebar-foreground/60 hover:text-sidebar-foreground transition-colors p-1 rounded-md hover:bg-sidebar-accent/20"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  transition={{ duration: 0.1 }}
                >
                  <X className="h-6 w-6" />
                </motion.button>
              </div>
              <nav className="flex-1 space-y-1 px-3 py-4">
                {navigation.map((item, index) => {
                  const isActive = pathname === item.href
                  return (
                    <motion.div
                      key={item.name}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1, duration: 0.3 }}
                    >
                      <Link
                        href={item.href}
                        className={`group flex items-center justify-between px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ${
                          isActive
                            ? 'bg-sidebar-accent text-sidebar-accent-foreground shadow-sm'
                            : 'text-sidebar-foreground/80 hover:bg-sidebar-accent/50 hover:text-sidebar-foreground'
                        }`}
                        onClick={() => setSidebarOpen(false)}
                      >
                        <motion.div
                          className="flex items-center"
                          {...navItemHover}
                        >
                          <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
                          {item.name}
                        </motion.div>
                        <div className="flex items-center space-x-2">
                          {item.badge && (
                            <motion.span
                              className="px-2 py-0.5 text-xs font-medium bg-primary/20 text-primary rounded-full"
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ delay: 0.5 + index * 0.1 }}
                            >
                              {item.badge}
                            </motion.span>
                          )}
                          {isActive && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ duration: 0.2 }}
                            >
                              <ChevronRight className="h-4 w-4" />
                            </motion.div>
                          )}
                        </div>
                      </Link>
                    </motion.div>
                  )
                })}
              </nav>
            </motion.div>
          </div>
        )}
      </AnimatePresence>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <motion.div
          className="flex flex-col flex-grow bg-gradient-to-b from-interior-soft-cream via-interior-warm-beige/50 to-interior-warm-beige border-r border-interior-warm-brown/20 shadow-xl"
          initial={{ x: -280 }}
          animate={{ x: 0 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        >
          <motion.div
            className="flex h-16 items-center px-4 border-b border-sidebar-border bg-gradient-to-r from-interior-warm-beige via-interior-soft-cream to-interior-warm-beige/90 shadow-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <motion.h1
              className="text-xl font-bold text-interior-deep-brown flex items-center"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <motion.div
                className="mr-3 w-8 h-8 bg-gradient-to-br from-interior-warm-brown to-interior-deep-brown rounded-lg flex items-center justify-center shadow-md"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.2 }}
              >
                <span className="text-interior-soft-cream text-sm">🏠</span>
              </motion.div>
              <span className="bg-gradient-to-r from-interior-deep-brown to-interior-warm-brown bg-clip-text text-transparent">
                Interior Admin
              </span>
            </motion.h1>
          </motion.div>

          <nav className="flex-1 space-y-1 px-3 py-4">
            {navigation.map((item, index) => {
              const isActive = pathname === item.href
              return (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 + index * 0.05, duration: 0.3 }}
                >
                  <Link
                    href={item.href}
                    className={`group flex items-center justify-between px-3 py-2.5 text-sm font-medium rounded-xl transition-all duration-200 relative overflow-hidden ${
                      isActive
                        ? 'bg-gradient-to-r from-interior-warm-brown to-interior-deep-brown text-interior-soft-cream shadow-lg'
                        : 'text-interior-deep-brown/80 hover:bg-gradient-to-r hover:from-interior-warm-beige hover:to-interior-warm-beige/80 hover:text-interior-deep-brown hover:shadow-md'
                    }`}
                  >
                    <motion.div
                      className="flex items-center"
                      {...navItemHover}
                    >
                      <motion.div
                        whileHover={{ scale: 1.1 }}
                        transition={{ duration: 0.2 }}
                      >
                        <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
                      </motion.div>
                      {item.name}
                    </motion.div>

                    <div className="flex items-center space-x-2">
                      {item.badge && (
                        <motion.span
                          className="px-2 py-0.5 text-xs font-medium bg-gradient-to-r from-interior-terracotta to-interior-warm-gold text-interior-soft-cream rounded-full shadow-sm"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ delay: 0.5 + index * 0.1 }}
                          whileHover={{ scale: 1.1 }}
                        >
                          {item.badge}
                        </motion.span>
                      )}
                      {isActive && (
                        <motion.div
                          initial={{ scale: 0, rotate: -90 }}
                          animate={{ scale: 1, rotate: 0 }}
                          transition={{ duration: 0.3, type: "spring" }}
                        >
                          <ChevronRight className="h-4 w-4 text-interior-soft-cream" />
                        </motion.div>
                      )}
                    </div>

                    {/* Active indicator */}
                    {isActive && (
                      <motion.div
                        className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-interior-warm-gold to-interior-terracotta rounded-r-full shadow-sm"
                        initial={{ scaleY: 0 }}
                        animate={{ scaleY: 1 }}
                        transition={{ duration: 0.3 }}
                      />
                    )}
                  </Link>
                </motion.div>
              )
            })}
          </nav>
          <motion.div
            className="border-t border-sidebar-border p-4 bg-gradient-to-r from-sidebar to-sidebar/95"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <motion.div
              className="flex items-center p-2 rounded-lg hover:bg-sidebar-accent/30 transition-colors duration-200"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <div className="flex-shrink-0">
                <motion.div
                  className="h-10 w-10 rounded-full bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center shadow-md"
                  whileHover={{ scale: 1.1 }}
                  transition={{ duration: 0.2 }}
                >
                  <span className="text-sm font-bold text-primary-foreground">
                    {session.user.name?.charAt(0).toUpperCase() || 'A'}
                  </span>
                </motion.div>
              </div>
              <div className="ml-3 flex-1">
                <p className="text-sm font-medium text-sidebar-foreground">{session.user.name}</p>
                <p className="text-xs text-sidebar-foreground/60 truncate">{session.user.email}</p>
              </div>
              <motion.div
                whileHover={{ scale: 1.1 }}
                transition={{ duration: 0.2 }}
              >
                <Bell className="h-4 w-4 text-sidebar-foreground/60" />
              </motion.div>
            </motion.div>

            <div className="mt-3 flex space-x-2">
              <motion.button
                onClick={handleLogout}
                className="flex-1 flex items-center justify-center px-3 py-2 text-xs text-sidebar-foreground/70 hover:text-sidebar-foreground transition-all duration-200 rounded-md hover:bg-destructive/10 hover:text-destructive group"
                {...buttonPress}
                whileHover={{ scale: 1.02 }}
              >
                <motion.div
                  className="mr-1"
                  whileHover={{ x: -2 }}
                  transition={{ duration: 0.2 }}
                >
                  <LogOut className="h-4 w-4" />
                </motion.div>
                Đăng xuất
              </motion.button>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <motion.div
          className="sticky top-0 z-40 flex h-16 bg-gradient-to-r from-interior-soft-cream/95 via-card/95 to-interior-warm-beige/95 backdrop-blur-sm shadow-lg border-b border-interior-warm-brown/20"
          initial={{ y: -64 }}
          animate={{ y: 0 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        >
          <motion.button
            onClick={() => setSidebarOpen(true)}
            className="px-4 text-muted-foreground hover:text-foreground focus:outline-none focus:ring-2 focus:ring-inset focus:ring-ring lg:hidden transition-colors duration-200"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            transition={{ duration: 0.1 }}
          >
            <Menu className="h-6 w-6" />
          </motion.button>

          <div className="flex flex-1 justify-between px-4 lg:px-6">
            <div className="flex flex-1 items-center">
              <motion.div
                className="flex w-full max-w-lg md:ml-0"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
              >
                <div className="relative w-full">
                  <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <Search className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <input
                    type="text"
                    placeholder="Tìm kiếm..."
                    className="block w-full rounded-xl border border-interior-warm-brown/20 bg-interior-soft-cream/50 py-2.5 pl-10 pr-3 text-sm placeholder:text-interior-warm-brown/60 focus:border-interior-warm-brown focus:outline-none focus:ring-2 focus:ring-interior-warm-brown/20 transition-all duration-200 shadow-sm"
                  />
                </div>
              </motion.div>
            </div>

            <motion.div
              className="ml-4 flex items-center space-x-4 md:ml-6"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              <motion.button
                className="relative p-2 text-muted-foreground hover:text-foreground transition-colors duration-200 rounded-lg hover:bg-accent/50"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Bell className="h-5 w-5" />
                <motion.span
                  className="absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full"
                  animate={{
                    scale: [1, 1.2, 1],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
              </motion.button>

              <div className="hidden sm:flex items-center space-x-3">
                <div className="h-9 w-9 rounded-xl bg-gradient-to-br from-interior-warm-brown to-interior-deep-brown flex items-center justify-center shadow-md">
                  <span className="text-xs font-bold text-interior-soft-cream">
                    {session.user.name?.charAt(0).toUpperCase() || 'A'}
                  </span>
                </div>
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-interior-deep-brown">
                    {session.user.name || 'Admin'}
                  </span>
                  <span className="text-xs text-interior-warm-brown/70">
                    Quản trị viên
                  </span>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Page content */}
        <main className="flex-1">
          <motion.div
            className="py-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.3 }}
          >
            {children}
          </motion.div>
        </main>
      </div>
    </motion.div>
  )
}

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <SessionProvider>
      <AdminLayoutContent>{children}</AdminLayoutContent>
    </SessionProvider>
  )
}
