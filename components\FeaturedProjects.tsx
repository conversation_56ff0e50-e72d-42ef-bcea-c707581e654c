"use client"
import Image from "next/image"
import { useFeaturedProjects } from "@/hooks/use-api"
import { Loader2, MapPin, Calendar, Square } from "lucide-react"

export default function FeaturedProjects() {
  const { data: projects, loading, error } = useFeaturedProjects(6)

  if (loading) {
    return (
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-[#1F2937] mb-4">DỰ ÁN NỔI BẬT</h2>
            <p className="text-xl text-gray-600">Những công trình tiêu biểu đã hoàn thành</p>
          </div>
          <div className="flex justify-center">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
          </div>
        </div>
      </section>
    )
  }

  if (error) {
    return (
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p className="text-red-600">Không thể tải dự án: {error}</p>
          </div>
        </div>
      </section>
    )
  }
  return (
    <section className="py-20 bg-[#F7F7F7]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-[#1F2937] mb-6 tracking-wider">DỰ ÁN NỔI BẬT</h2>
          <p className="text-xl text-[#6B7280] max-w-3xl mx-auto">
            Khám phá những dự án thiết kế nội thất đẳng cấp mà chúng tôi đã thực hiện
          </p>
        </div>

        {projects && projects.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {projects.map((project: any, index: number) => (
              <div key={project.id} className={`group relative overflow-hidden rounded-xl shadow-lg cursor-pointer ${index === 0 ? 'md:col-span-2 lg:col-span-2' : ''}`}>
                <div className={`relative ${index === 0 ? 'h-[400px]' : 'h-[300px]'}`}>
                  <Image
                    src={project.image || 'https://images.unsplash.com/photo-1600210492486-724fe5c67fb0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600'}
                    alt={project.title}
                    fill
                    className="object-cover transition-all duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />

                  <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                    <div className="flex items-center gap-2 mb-2">
                      {project.category_name && (
                        <span className="bg-blue-600/80 text-xs px-2 py-1 rounded-full">
                          {project.category_name}
                        </span>
                      )}
                      {project.area && (
                        <span className="flex items-center gap-1 text-xs opacity-90">
                          <Square className="w-3 h-3" />
                          {project.area}
                        </span>
                      )}
                    </div>

                    <h3 className={`font-bold mb-2 ${index === 0 ? 'text-2xl' : 'text-xl'}`}>
                      {project.title}
                    </h3>

                    {project.location && (
                      <p className="flex items-center gap-1 text-sm opacity-90 mb-2">
                        <MapPin className="w-4 h-4" />
                        {project.location}
                      </p>
                    )}

                    {project.completed_date && (
                      <p className="flex items-center gap-1 text-sm opacity-75">
                        <Calendar className="w-4 h-4" />
                        {new Date(project.completed_date).toLocaleDateString('vi-VN')}
                      </p>
                    )}

                    <div className="mt-4 opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300">
                      <span className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-semibold transition-colors">
                        Xem chi tiết
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500">Chưa có dự án nào được hiển thị</p>
          </div>
        )}

        <div className="text-center mt-12">
          <button className="bg-[#3B82F6] hover:bg-[#2563EB] text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors">
            XEM TẤT CẢ DỰ ÁN
          </button>
        </div>
      </div>
    </section>
  )
}
