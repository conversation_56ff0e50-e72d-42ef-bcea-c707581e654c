type Route = {
  url: string;
  lastModified: string;
  changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority: number;
};

export function generateSitemap(baseUrl: string, routes: Route[]) {
  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${routes
  .map((route) => {
    return `  <url>
    <loc>${baseUrl}${route.url}</loc>
    <lastmod>${route.lastModified}</lastmod>
    <changefreq>${route.changeFrequency}</changefreq>
    <priority>${route.priority}</priority>
  </url>`;
  })
  .join('\n')}
</urlset>`;
}

export function getSitemapRoutes(): Route[] {
  const today = new Date().toISOString().split('T')[0];

  return [
    { url: '/', lastModified: today, changeFrequency: 'weekly', priority: 1.0 },
    { url: '/dich-vu', lastModified: today, changeFrequency: 'weekly', priority: 0.8 },
    { url: '/du-an-da-hoan-thien', lastModified: today, changeFrequency: 'weekly', priority: 0.8 },
    { url: '/cam-nang', lastModified: today, changeFrequency: 'weekly', priority: 0.8 },
    { url: '/chia-se-kinh-nghiem', lastModified: today, changeFrequency: 'weekly', priority: 0.8 },
    { url: '/video-cong-trinh-hoan-thien', lastModified: today, changeFrequency: 'weekly', priority: 0.8 },
    { url: '/gioi-thieu', lastModified: today, changeFrequency: 'monthly', priority: 0.7 },
    { url: '/gui-yeu-cau', lastModified: today, changeFrequency: 'monthly', priority: 0.7 },
    // Thêm các route động khi có dữ liệu thực tế
  ];
}
