"use client"

import { useState } from "react"
import { Play, Award, Users, Clock, Star } from "lucide-react"

export default function Introduction() {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false)

  const achievements = [
    { icon: Award, number: "15+", label: "<PERSON>ă<PERSON>" },
    { icon: Users, number: "1000+", label: "Kh<PERSON>ch <PERSON>" },
    { icon: Clock, number: "500+", label: "Dự Á<PERSON>" },
    { icon: Star, number: "4.9/5", label: "Đánh G<PERSON>á Trung Bình" }
  ]

  return (
    <section className="relative py-20 overflow-hidden bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Animated Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div 
          className="absolute inset-0 animate-pulse"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}
        ></div>
      </div>

      <div className="relative container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center min-h-[700px]">
          {/* Left side - Enhanced Text content */}
          <div className="text-white space-y-8">
            <div className="space-y-4">
              <div className="inline-flex items-center gap-2 bg-blue-600/20 text-blue-300 px-4 py-2 rounded-full text-sm font-medium">
                <Star className="w-4 h-4" />
                Đối Tác Tin Cậy Hàng Đầu
              </div>
              <h2 className="text-5xl md:text-6xl font-bold tracking-tight bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                KIẾN TRÚC SƯ
                <br />
                <span className="text-blue-400">THÁI VŨ</span>
              </h2>
              <p className="text-xl text-gray-300 leading-relaxed">
                Tạo nên không gian sống hoàn hảo với hơn 15 năm kinh nghiệm
              </p>
            </div>

            <div className="text-lg leading-relaxed space-y-6 text-gray-200">
              <p className="relative pl-6">
                <span className="absolute left-0 top-2 w-2 h-2 bg-blue-400 rounded-full"></span>
                Chuyên gia hàng đầu trong lĩnh vực thiết kế nội thất cao cấp, với portfolio hơn <strong className="text-white">500 dự án</strong> thành công trên toàn quốc.
              </p>
              <p className="relative pl-6">
                <span className="absolute left-0 top-2 w-2 h-2 bg-blue-400 rounded-full"></span>
                Đại lý chính thức các thương hiệu bếp Nhật Bản cao cấp, mang đến giải pháp nội thất <strong className="text-white">hiện đại và bền vững</strong>.
              </p>
              <p className="relative pl-6">
                <span className="absolute left-0 top-2 w-2 h-2 bg-blue-400 rounded-full"></span>
                Đội ngũ kiến trúc sư tài năng, cam kết <strong className="text-white">chất lượng tuyệt đối</strong> và dịch vụ hậu mãi trọn đời.
              </p>
            </div>

            {/* Achievement Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 py-8">
              {achievements.map((achievement, index) => (
                <div key={index} className="text-center group">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-600/20 rounded-lg mb-3 group-hover:bg-blue-600/30 transition-colors">
                    <achievement.icon className="w-6 h-6 text-blue-400" />
                  </div>
                  <div className="text-2xl font-bold text-white">{achievement.number}</div>
                  <div className="text-sm text-gray-400">{achievement.label}</div>
                </div>
              ))}
            </div>

            <div className="flex flex-col sm:flex-row gap-4 pt-4">
              <button className="group bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                <span className="flex items-center justify-center gap-2">
                  XEM PORTFOLIO
                  <Award className="w-5 h-5 group-hover:rotate-12 transition-transform" />
                </span>
              </button>
              <button className="group border-2 border-white/30 text-white hover:bg-white hover:text-slate-900 px-8 py-4 rounded-xl font-semibold text-lg bg-transparent transition-all duration-300 backdrop-blur-sm">
                <span className="flex items-center justify-center gap-2">
                  TƯ VẤN MIỄN PHÍ
                  <Users className="w-5 h-5 group-hover:scale-110 transition-transform" />
                </span>
              </button>
            </div>
          </div>

          {/* Right side - Enhanced Video/Interactive Content */}
          <div className="relative">
            {/* Main Video/Image Container */}
            <div className="relative group">
              <div className="relative overflow-hidden rounded-2xl shadow-2xl">
                <div
                  className="w-full h-[500px] bg-cover bg-center transition-transform duration-700 group-hover:scale-105"
                  style={{
                    backgroundImage: `url('https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600')`,
                  }}
                >
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />

                  {/* Video Play Button */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <button
                      onClick={() => setIsVideoPlaying(true)}
                      className="group/play bg-white/20 backdrop-blur-md hover:bg-white/30 rounded-full p-6 transition-all duration-300 transform hover:scale-110"
                    >
                      <Play className="w-12 h-12 text-white ml-1 group-hover/play:scale-110 transition-transform" />
                    </button>
                  </div>

                  {/* Floating Info Card */}
                  <div className="absolute bottom-6 left-6 right-6">
                    <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                          <span className="text-2xl font-bold text-white">TV</span>
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-white">Kiến Trúc Sư Thái Vũ</h3>
                          <p className="text-blue-200">Founder & Lead Designer</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-sm text-white/90">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                          <span>Chứng chỉ quốc tế</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                          <span>Đại lý Nhật Bản</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                          <span>Thi công A-Z</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                          <span>Bảo hành trọn đời</span>
                        </div>
                      </div>

                      <div className="mt-4 pt-4 border-t border-white/20">
                        <div className="flex items-center justify-between">
                          <span className="text-white/80">Hotline 24/7:</span>
                          <a href="tel:0123456789" className="font-bold text-blue-300 hover:text-blue-200 transition-colors">
                            0123 456 789
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Decorative Elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-blue-500/20 rounded-full blur-xl"></div>
              <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-purple-500/20 rounded-full blur-xl"></div>
            </div>

            {/* Video Modal */}
            {isVideoPlaying && (
              <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
                <div className="relative w-full max-w-4xl aspect-video">
                  <button
                    onClick={() => setIsVideoPlaying(false)}
                    className="absolute -top-12 right-0 text-white hover:text-gray-300 text-xl"
                  >
                    ✕ Đóng
                  </button>
                  <div className="w-full h-full bg-gray-800 rounded-lg flex items-center justify-center">
                    <p className="text-white">Video demo sẽ được thêm vào đây</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  )
}
