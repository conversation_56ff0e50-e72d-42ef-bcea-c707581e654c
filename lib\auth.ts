import { NextAuthOptions } from 'next-auth'
import Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'

// Simple admin credentials (in production, use database)
const ADMIN_CREDENTIALS = {
  email: process.env.ADMIN_EMAIL || '<EMAIL>',
  password: process.env.ADMIN_PASSWORD || 'admin123'
}

export const authOptions: NextAuthOptions = {
  debug: true, // Enable debug mode
  useSecureCookies: false, // Disable secure cookies for localhost
  skipCSRFCheck: true, // Temporarily disable CSRF check for debugging
  providers: [
    CredentialsProvider({
      id: 'credentials',
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials, req) {
        console.log('🔐 Auth attempt started')
        console.log('🔐 Received credentials:', {
          email: credentials?.email,
          password: credentials?.password ? '***' : 'MISSING',
          hasCredentials: !!credentials
        })
        console.log('🔐 Expected credentials:', {
          email: ADMIN_CREDENTIALS.email,
          password: ADMIN_CREDENTIALS.password ? '***' : 'MISSING'
        })

        if (!credentials?.email || !credentials?.password) {
          console.log('❌ Missing credentials')
          return null
        }

        // Simple credential check (in production, hash passwords)
        const emailMatch = credentials.email === ADMIN_CREDENTIALS.email
        const passwordMatch = credentials.password === ADMIN_CREDENTIALS.password

        console.log('🔐 Credential validation:', {
          emailMatch,
          passwordMatch,
          receivedEmail: credentials.email,
          expectedEmail: ADMIN_CREDENTIALS.email
        })

        if (emailMatch && passwordMatch) {
          console.log('✅ Credentials match, creating user session')
          const user = {
            id: '1',
            email: ADMIN_CREDENTIALS.email,
            name: 'Admin',
            role: 'admin'
          }
          console.log('✅ Returning user:', user)
          return user
        }

        console.log('❌ Credentials do not match')
        return null
      }
    })
  ],
  session: {
    strategy: 'jwt'
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub
        session.user.role = token.role
      }
      return session
    }
  },
  pages: {
    signIn: '/admin/login'
  },
  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key'
}
