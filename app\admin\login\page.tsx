"use client"

import { useState } from 'react'
import { signIn, getSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Lock, Mail, Building2 } from 'lucide-react'

export default function AdminLogin() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      })

      if (result?.error) {
        setError('Thông tin đăng nhập không hợp lệ')
      } else {
        const session = await getSession()
        if (session) {
          router.push('/admin/dashboard')
        }
      }
    } catch (error) {
      setError('Đăng nhập thất bại. Vui lòng thử lại.')
    } finally {
      setLoading(false)
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
      },
    },
  }

  return (
    <div className="min-h-screen w-full lg:grid lg:grid-cols-2">
      {/* Left Panel - Branding */}
      <div className="hidden bg-gradient-to-br from-interior-soft-cream via-interior-warm-beige to-interior-warm-beige/80 lg:flex flex-col items-center justify-center p-12 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-20 w-32 h-32 bg-interior-warm-brown rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-20 w-40 h-40 bg-interior-sage-green rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-interior-terracotta rounded-full blur-3xl"></div>
        </div>

        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5, ease: 'easeOut' }}
          className="flex flex-col items-center text-center relative z-10"
        >
          <motion.div
            className="relative mb-8"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.3 }}
          >
            <div className="absolute inset-0 bg-gradient-to-br from-interior-warm-gold to-interior-terracotta rounded-full blur-xl opacity-30"></div>
            <Building2 className="h-24 w-24 text-interior-deep-brown relative z-10" />
          </motion.div>
          <h1 className="text-4xl font-bold tracking-tight text-interior-deep-brown mb-4">
            Interior Admin Panel
          </h1>
          <p className="text-lg text-interior-warm-brown/80 max-w-md">
            Quản lý toàn diện cho website nội thất của bạn với giao diện chuyên nghiệp.
          </p>
          <div className="mt-8 flex items-center space-x-4 text-sm text-interior-warm-brown/60">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-interior-sage-green rounded-full animate-pulse"></div>
              <span>Hệ thống hoạt động</span>
            </div>
            <div className="w-1 h-4 bg-interior-warm-brown/20"></div>
            <span>Bảo mật cao</span>
          </div>
        </motion.div>
      </div>

      {/* Right Panel - Login Form */}
      <div className="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-background via-card to-background/95 relative">
        {/* Subtle background pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 right-10 w-20 h-20 bg-interior-sage-green rounded-full blur-2xl"></div>
          <div className="absolute bottom-10 left-10 w-16 h-16 bg-interior-warm-gold rounded-full blur-2xl"></div>
        </div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mx-auto w-full max-w-md space-y-8 relative z-10"
        >
          <motion.div variants={itemVariants} className="space-y-3 text-center">
            <div className="mx-auto w-16 h-16 bg-gradient-to-br from-interior-warm-brown to-interior-deep-brown rounded-2xl flex items-center justify-center mb-6 shadow-lg">
              <Lock className="h-8 w-8 text-interior-soft-cream" />
            </div>
            <h1 className="text-3xl font-bold text-foreground">Chào mừng trở lại!</h1>
            <p className="text-muted-foreground text-base">
              Đăng nhập để tiếp tục quản lý hệ thống nội thất.
            </p>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="bg-card/80 backdrop-blur-sm border border-border/50 rounded-2xl p-8 shadow-xl"
          >
            <form onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-destructive/10 border border-destructive/20 rounded-lg p-3"
                >
                  <p className="text-destructive text-sm font-medium">{error}</p>
                </motion.div>
              )}

              <div className="space-y-2">
                <Label htmlFor="email" className="text-foreground font-medium">Email</Label>
                <div className="relative group">
                  <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground group-focus-within:text-interior-warm-brown transition-colors" />
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    disabled={loading}
                    placeholder="<EMAIL>"
                    className="pl-10 h-12 border-border/50 focus:border-interior-warm-brown focus:ring-interior-warm-brown/20 bg-background/50 transition-all duration-200"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password" className="text-foreground font-medium">Mật khẩu</Label>
                <div className="relative group">
                  <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground group-focus-within:text-interior-warm-brown transition-colors" />
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    disabled={loading}
                    placeholder="••••••••"
                    className="pl-10 h-12 border-border/50 focus:border-interior-warm-brown focus:ring-interior-warm-brown/20 bg-background/50 transition-all duration-200"
                  />
                </div>
              </div>

              <motion.div
                whileHover={{ scale: 1.01 }}
                whileTap={{ scale: 0.99 }}
              >
                <Button
                  type="submit"
                  className="w-full h-12 bg-gradient-to-r from-interior-warm-brown to-interior-deep-brown hover:from-interior-deep-brown hover:to-interior-warm-brown text-interior-soft-cream font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Đang xử lý...
                    </>
                  ) : (
                    'Đăng Nhập'
                  )}
                </Button>
              </motion.div>
            </form>
          </motion.div>

          <motion.div variants={itemVariants} className="text-center">
            <div className="bg-muted/30 rounded-lg p-4 border border-border/30">
              <p className="text-sm text-muted-foreground mb-2">Thông tin đăng nhập demo:</p>
              <div className="space-y-1 text-sm">
                <p><strong className="text-interior-warm-brown">Email:</strong> <code className="bg-background/50 px-2 py-1 rounded text-foreground"><EMAIL></code></p>
                <p><strong className="text-interior-warm-brown">Mật khẩu:</strong> <code className="bg-background/50 px-2 py-1 rounded text-foreground">admin123</code></p>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}
