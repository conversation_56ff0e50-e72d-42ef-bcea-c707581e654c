"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Plus, Search, MoreHorizontal, Edit, Trash2, Eye, Briefcase, Loader2, AlertTriangle, RefreshCw, Grid, List, Star, Calendar, Filter } from 'lucide-react'
import { motion, AnimatePresence } from "framer-motion"
import { apiClient } from '@/lib/api-client'
import Link from 'next/link'
import {
  fadeInUp,
  staggerContainer,
  staggerItem,
  cardHover,
  buttonPress,
  getMotionProps
} from "@/lib/motion"

interface Service {
  id: number
  title: string
  slug: string
  description?: string
  category_name?: string
  featured: boolean
  status: 'draft' | 'published' | 'archived'
  created_at: string
}

export default function ServicesAdmin() {
  const [services, setServices] = useState<Service[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [deleting, setDeleting] = useState<number | null>(null)
  const [deleteConfirm, setDeleteConfirm] = useState<number | null>(null)

  useEffect(() => {
    fetchServices()
  }, [])

  const fetchServices = async () => {
    try {
      const response = await apiClient.getServices()
      if (response.success) {
        setServices(response.data || [])
      }
    } catch (error) {
      console.error('Error fetching services:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredServices = services.filter(service =>
    service.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    service.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleDelete = async (serviceId: number) => {
    setDeleting(serviceId)
    try {
      const response = await apiClient.deleteService(serviceId)
      if (response.success) {
        setServices(services.filter(service => service.id !== serviceId))
        setDeleteConfirm(null)
      } else {
        console.error('Failed to delete service:', response.error)
      }
    } catch (error) {
      console.error('Error deleting service:', error)
    } finally {
      setDeleting(null)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge className="bg-gradient-to-r from-interior-sage-green/20 to-interior-forest-green/20 text-interior-forest-green border-interior-sage-green/30 shadow-sm">Đã xuất bản</Badge>
      case 'draft':
        return <Badge className="bg-gradient-to-r from-interior-warm-gold/20 to-interior-terracotta/20 text-interior-terracotta border-interior-warm-gold/30 shadow-sm">Bản nháp</Badge>
      case 'archived':
        return <Badge className="bg-gradient-to-r from-interior-warm-gray/20 to-interior-charcoal/20 text-interior-charcoal border-interior-warm-gray/30 shadow-sm">Đã lưu trữ</Badge>
      default:
        return <Badge className="bg-gradient-to-r from-interior-warm-beige to-interior-soft-cream text-interior-deep-brown border-interior-warm-brown/30">{status}</Badge>
    }
  }

  if (loading) {
    return (
      <motion.div 
        className="flex flex-col items-center justify-center h-64 space-y-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <motion.div
          className="relative"
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        >
          <div className="w-16 h-16 border-4 border-interior-warm-beige rounded-full"></div>
          <div className="absolute top-0 left-0 w-16 h-16 border-4 border-transparent border-t-interior-warm-brown rounded-full"></div>
        </motion.div>
        <motion.p 
          className="text-interior-warm-brown font-medium"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          Đang tải dịch vụ...
        </motion.p>
      </motion.div>
    )
  }

  return (
    <motion.div
      className="px-4 sm:px-6 lg:px-8 py-6 bg-gradient-to-br from-background via-interior-soft-cream/30 to-background min-h-screen"
      {...getMotionProps(fadeInUp)}
    >
      {/* Header */}
      <motion.div
        className="sm:flex sm:items-center bg-gradient-to-r from-interior-soft-cream/50 to-interior-warm-beige/30 rounded-2xl p-6 shadow-lg border border-interior-warm-brown/10 mb-8"
        {...getMotionProps(fadeInUp)}
      >
        <div className="sm:flex-auto">
          <motion.h1
            className="text-3xl font-bold text-interior-deep-brown flex items-center"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
          >
            <motion.div
              className="mr-4 p-3 bg-gradient-to-br from-interior-warm-brown to-interior-deep-brown rounded-xl shadow-md"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ duration: 0.2 }}
            >
              <Briefcase className="h-8 w-8 text-interior-soft-cream" />
            </motion.div>
            Quản Lý Dịch Vụ
          </motion.h1>
          <motion.p
            className="mt-3 text-base text-interior-warm-brown/80"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            Quản lý các dịch vụ thiết kế nội thất và thi công chuyên nghiệp
          </motion.p>
          <motion.div
            className="mt-4 flex items-center space-x-6 text-sm text-interior-warm-brown/60"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-interior-sage-green rounded-full animate-pulse"></div>
              <span>Tổng: {services.length} dịch vụ</span>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <span>Cập nhật: {new Date().toLocaleDateString('vi-VN')}</span>
            </div>
          </motion.div>
        </div>
        <motion.div
          className="mt-6 sm:mt-0 sm:ml-16 sm:flex-none space-x-3 flex flex-wrap gap-2"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          <motion.button
            onClick={fetchServices}
            className="p-3 text-interior-warm-brown hover:text-interior-deep-brown transition-colors duration-200 rounded-xl hover:bg-interior-warm-beige/50 shadow-sm border border-interior-warm-brown/20"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
            title="Làm mới"
          >
            <RefreshCw className="h-5 w-5" />
          </motion.button>

          <Link href="/admin/services/new">
            <motion.div {...buttonPress}>
              <Button className="bg-gradient-to-r from-interior-warm-brown to-interior-deep-brown hover:from-interior-deep-brown hover:to-interior-warm-brown text-interior-soft-cream shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-3 rounded-xl">
                <Plus className="h-5 w-5 mr-2" />
                Thêm Dịch Vụ
              </Button>
            </motion.div>
          </Link>
        </motion.div>
      </motion.div>

      {/* Search and Filters */}
      <motion.div
        className="mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <div className="bg-gradient-to-r from-interior-soft-cream/40 to-interior-warm-beige/20 rounded-xl p-6 shadow-md border border-interior-warm-brown/10">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-interior-warm-brown/60 h-5 w-5" />
              <Input
                placeholder="Tìm kiếm dịch vụ theo tên, mô tả..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-12 h-12 bg-interior-soft-cream/50 border-interior-warm-brown/20 focus:border-interior-warm-brown focus:ring-interior-warm-brown/20 rounded-xl shadow-sm transition-all duration-200 text-interior-deep-brown placeholder:text-interior-warm-brown/60"
              />
            </div>
            <div className="flex items-center space-x-3">
              <motion.div
                className="flex items-center space-x-2 text-sm text-interior-warm-brown/70"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
              >
                <Filter className="h-4 w-4" />
                <span>Tìm thấy: {filteredServices.length} dịch vụ</span>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Delete Confirmation */}
      <AnimatePresence>
        {deleteConfirm && (
          <motion.div
            className="mt-6"
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            <Alert variant="destructive" className="border-destructive/20 bg-destructive/5">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.1, type: "spring" }}
              >
                <AlertTriangle className="h-4 w-4" />
              </motion.div>
              <AlertDescription className="flex items-center justify-between">
                <span>Bạn có chắc chắn muốn xóa dịch vụ này? Hành động này không thể hoàn tác.</span>
                <div className="flex space-x-2 ml-4">
                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setDeleteConfirm(null)}
                      disabled={deleting === deleteConfirm}
                      className="hover:bg-accent/50"
                    >
                      Hủy
                    </Button>
                  </motion.div>
                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleDelete(deleteConfirm)}
                      disabled={deleting === deleteConfirm}
                      className="shadow-md hover:shadow-lg transition-shadow duration-200"
                    >
                      {deleting === deleteConfirm ? (
                        <motion.div
                          className="flex items-center"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                        >
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          Đang xóa...
                        </motion.div>
                      ) : (
                        'Xóa'
                      )}
                    </Button>
                  </motion.div>
                </div>
              </AlertDescription>
            </Alert>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Table */}
      <motion.div
        className="flow-root"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
            <motion.div
              className="overflow-hidden shadow-xl ring-1 ring-interior-warm-brown/20 rounded-2xl bg-gradient-to-br from-interior-soft-cream/80 to-interior-warm-beige/60 backdrop-blur-sm hover:shadow-2xl transition-all duration-300"
              whileHover={{ y: -4, scale: 1.001 }}
              transition={{ duration: 0.3 }}
            >
              <Table>
                <TableHeader>
                  <TableRow className="border-interior-warm-brown/20 bg-gradient-to-r from-interior-warm-beige/50 to-interior-soft-cream/50">
                    <TableHead className="text-interior-deep-brown font-bold py-5 text-sm uppercase tracking-wide">Tiêu Đề</TableHead>
                    <TableHead className="text-interior-deep-brown font-bold py-5 text-sm uppercase tracking-wide">Danh Mục</TableHead>
                    <TableHead className="text-interior-deep-brown font-bold py-5 text-sm uppercase tracking-wide">Trạng Thái</TableHead>
                    <TableHead className="text-interior-deep-brown font-bold py-5 text-sm uppercase tracking-wide">Nổi Bật</TableHead>
                    <TableHead className="text-interior-deep-brown font-bold py-5 text-sm uppercase tracking-wide">Ngày Tạo</TableHead>
                    <TableHead className="relative py-5">
                      <span className="sr-only">Thao tác</span>
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <motion.tbody {...getMotionProps(staggerContainer)}>
                  {filteredServices.map((service, index) => (
                    <motion.tr
                      key={service.id}
                      className="border-interior-warm-brown/10 hover:bg-gradient-to-r hover:from-interior-warm-beige/30 hover:to-interior-soft-cream/30 transition-all duration-300"
                      {...getMotionProps(staggerItem)}
                      whileHover={{ 
                        backgroundColor: "hsl(var(--interior-warm-beige) / 0.2)",
                        scale: 1.001,
                        x: 4
                      }}
                    >
                      <TableCell className="py-4">
                        <motion.div
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.05 }}
                        >
                          <div className="font-medium text-foreground">{service.title}</div>
                          {service.description && (
                            <div className="text-sm text-muted-foreground truncate max-w-xs mt-1">
                              {service.description}
                            </div>
                          )}
                        </motion.div>
                      </TableCell>
                      <TableCell className="py-4">
                        {service.category_name && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: index * 0.05 + 0.1 }}
                          >
                            <Badge variant="outline" className="hover:bg-accent/50 transition-colors duration-200">
                              {service.category_name}
                            </Badge>
                          </motion.div>
                        )}
                      </TableCell>
                      <TableCell className="py-4">
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ delay: index * 0.05 + 0.2 }}
                        >
                          {getStatusBadge(service.status)}
                        </motion.div>
                      </TableCell>
                      <TableCell className="py-4">
                        {service.featured && (
                          <motion.div
                            initial={{ scale: 0, rotate: -180 }}
                            animate={{ scale: 1, rotate: 0 }}
                            transition={{ delay: index * 0.05 + 0.3, type: "spring" }}
                          >
                            <Badge className="bg-gradient-to-r from-interior-warm-gold/20 to-interior-terracotta/20 text-interior-terracotta border-interior-warm-gold/40 shadow-sm hover:from-interior-warm-gold/30 hover:to-interior-terracotta/30 transition-all duration-200 font-semibold">
                              <Star className="h-3 w-3 mr-1" />
                              Nổi bật
                            </Badge>
                          </motion.div>
                        )}
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground py-4">
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ delay: index * 0.05 + 0.4 }}
                        >
                          {new Date(service.created_at).toLocaleDateString('vi-VN')}
                        </motion.div>
                      </TableCell>
                      <TableCell className="py-4">
                        <motion.div
                          initial={{ opacity: 0, scale: 0 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: index * 0.05 + 0.5 }}
                        >
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <motion.div
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                              >
                                <Button
                                  variant="ghost"
                                  className="h-8 w-8 p-0 hover:bg-accent/50 transition-colors duration-200"
                                >
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </motion.div>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-48">
                              <DropdownMenuItem asChild>
                                <Link
                                  href={`/admin/services/${service.id}`}
                                  className="flex items-center"
                                >
                                  <Eye className="mr-2 h-4 w-4" />
                                  Xem chi tiết
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <Link
                                  href={`/admin/services/${service.id}/edit`}
                                  className="flex items-center"
                                >
                                  <Edit className="mr-2 h-4 w-4" />
                                  Chỉnh sửa
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                className="text-destructive hover:bg-destructive/10 transition-colors duration-200 focus:bg-destructive/10"
                                onClick={() => setDeleteConfirm(service.id)}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Xóa
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </motion.div>
                      </TableCell>
                    </motion.tr>
                  ))}
                </motion.tbody>
              </Table>
            </motion.div>
          </div>
        </div>
      </motion.div>

      {/* Empty State */}
      <AnimatePresence>
        {filteredServices.length === 0 && !loading && (
          <motion.div
            className="text-center py-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring" }}
              className="mx-auto h-12 w-12 text-muted-foreground mb-4"
            >
              <Briefcase className="h-12 w-12" />
            </motion.div>
            <p className="text-muted-foreground text-lg">
              {searchTerm ? 'Không tìm thấy dịch vụ nào.' : 'Chưa có dịch vụ nào.'}
            </p>
            {!searchTerm && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="mt-4"
              >
                <Link href="/admin/services/new">
                  <Button className="bg-primary hover:bg-primary/90">
                    <Plus className="h-4 w-4 mr-2" />
                    Tạo dịch vụ đầu tiên
                  </Button>
                </Link>
              </motion.div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
