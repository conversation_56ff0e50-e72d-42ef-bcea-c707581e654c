import { executeQuery, generateSlug } from '../database'

export interface Experience {
  id?: number
  title: string
  slug: string
  excerpt?: string
  content?: string
  image?: string
  author?: string
  author_avatar?: string
  category_id?: number
  featured?: boolean
  views?: number
  likes?: number
  read_time?: string
  status?: 'draft' | 'published' | 'archived'
  published_date?: string
  created_at?: string
  updated_at?: string
  // Joined fields
  category_name?: string
}

export class ExperienceModel {
  // Get all experiences with optional filters
  static async getAll(filters: {
    category_id?: number
    featured?: boolean
    status?: string
    limit?: number
    offset?: number
  } = {}): Promise<Experience[]> {
    let query = `
      SELECT e.*, c.name as category_name
      FROM experiences e
      LEFT JOIN categories c ON e.category_id = c.id
      WHERE 1=1
    `
    const params: any[] = []

    if (filters.category_id) {
      query += ' AND e.category_id = ?'
      params.push(filters.category_id)
    }

    if (filters.featured !== undefined) {
      query += ' AND e.featured = ?'
      params.push(filters.featured)
    }

    if (filters.status) {
      query += ' AND e.status = ?'
      params.push(filters.status)
    } else {
      query += ' AND e.status = "published"'
    }

    query += ' ORDER BY e.featured DESC, e.published_date DESC, e.created_at DESC'

    if (filters.limit) {
      query += ' LIMIT ?'
      params.push(filters.limit)
      
      if (filters.offset) {
        query += ' OFFSET ?'
        params.push(filters.offset)
      }
    }

    return executeQuery<Experience>(query, params)
  }

  // Get experience by ID
  static async getById(id: number): Promise<Experience | null> {
    const query = `
      SELECT e.*, c.name as category_name
      FROM experiences e
      LEFT JOIN categories c ON e.category_id = c.id
      WHERE e.id = ?
    `
    const results = await executeQuery<Experience>(query, [id])
    return results.length > 0 ? results[0] : null
  }

  // Get experience by slug
  static async getBySlug(slug: string): Promise<Experience | null> {
    const query = `
      SELECT e.*, c.name as category_name
      FROM experiences e
      LEFT JOIN categories c ON e.category_id = c.id
      WHERE e.slug = ? AND e.status = 'published'
    `
    const results = await executeQuery<Experience>(query, [slug])
    return results.length > 0 ? results[0] : null
  }

  // Create new experience
  static async create(data: Omit<Experience, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    const slug = data.slug || generateSlug(data.title)
    
    const query = `
      INSERT INTO experiences (
        title, slug, excerpt, content, image, author, author_avatar,
        category_id, featured, views, likes, read_time, status, published_date
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `
    
    const result = await executeQuery(query, [
      data.title,
      slug,
      data.excerpt || null,
      data.content || null,
      data.image || null,
      data.author || null,
      data.author_avatar || null,
      data.category_id || null,
      data.featured || false,
      data.views || 0,
      data.likes || 0,
      data.read_time || null,
      data.status || 'published',
      data.published_date || new Date().toISOString()
    ])
    
    return (result as any).insertId
  }

  // Update experience
  static async update(id: number, data: Partial<Omit<Experience, 'id' | 'created_at' | 'updated_at'>>): Promise<boolean> {
    const fields: string[] = []
    const values: any[] = []

    if (data.title !== undefined) {
      fields.push('title = ?')
      values.push(data.title)
    }

    if (data.slug !== undefined) {
      fields.push('slug = ?')
      values.push(data.slug)
    }

    if (data.excerpt !== undefined) {
      fields.push('excerpt = ?')
      values.push(data.excerpt)
    }

    if (data.content !== undefined) {
      fields.push('content = ?')
      values.push(data.content)
    }

    if (data.image !== undefined) {
      fields.push('image = ?')
      values.push(data.image)
    }

    if (data.author !== undefined) {
      fields.push('author = ?')
      values.push(data.author)
    }

    if (data.author_avatar !== undefined) {
      fields.push('author_avatar = ?')
      values.push(data.author_avatar)
    }

    if (data.category_id !== undefined) {
      fields.push('category_id = ?')
      values.push(data.category_id)
    }

    if (data.featured !== undefined) {
      fields.push('featured = ?')
      values.push(data.featured)
    }

    if (data.views !== undefined) {
      fields.push('views = ?')
      values.push(data.views)
    }

    if (data.likes !== undefined) {
      fields.push('likes = ?')
      values.push(data.likes)
    }

    if (data.read_time !== undefined) {
      fields.push('read_time = ?')
      values.push(data.read_time)
    }

    if (data.status !== undefined) {
      fields.push('status = ?')
      values.push(data.status)
    }

    if (data.published_date !== undefined) {
      fields.push('published_date = ?')
      values.push(data.published_date)
    }

    if (fields.length === 0) {
      return false
    }

    values.push(id)

    const query = `
      UPDATE experiences 
      SET ${fields.join(', ')}
      WHERE id = ?
    `
    
    const result = await executeQuery(query, values)
    return (result as any).affectedRows > 0
  }

  // Delete experience
  static async delete(id: number): Promise<boolean> {
    const query = `
      DELETE FROM experiences 
      WHERE id = ?
    `
    const result = await executeQuery(query, [id])
    return (result as any).affectedRows > 0
  }

  // Get featured experiences
  static async getFeatured(limit: number = 6): Promise<Experience[]> {
    return this.getAll({ featured: true, limit })
  }

  // Search experiences
  static async search(searchTerm: string, limit: number = 10): Promise<Experience[]> {
    const query = `
      SELECT e.*, c.name as category_name
      FROM experiences e
      LEFT JOIN categories c ON e.category_id = c.id
      WHERE e.status = 'published' 
        AND (e.title LIKE ? OR e.excerpt LIKE ? OR e.content LIKE ?)
      ORDER BY e.featured DESC, e.published_date DESC, e.created_at DESC
      LIMIT ?
    `
    
    const searchPattern = `%${searchTerm}%`
    return executeQuery<Experience>(query, [searchPattern, searchPattern, searchPattern, limit])
  }

  // Increment view count
  static async incrementViews(id: number): Promise<boolean> {
    const query = `
      UPDATE experiences 
      SET views = views + 1
      WHERE id = ?
    `
    const result = await executeQuery(query, [id])
    return (result as any).affectedRows > 0
  }

  // Increment like count
  static async incrementLikes(id: number): Promise<boolean> {
    const query = `
      UPDATE experiences 
      SET likes = likes + 1
      WHERE id = ?
    `
    const result = await executeQuery(query, [id])
    return (result as any).affectedRows > 0
  }

  // Get popular experiences
  static async getPopular(limit: number = 10): Promise<Experience[]> {
    const query = `
      SELECT e.*, c.name as category_name
      FROM experiences e
      LEFT JOIN categories c ON e.category_id = c.id
      WHERE e.status = 'published'
      ORDER BY e.views DESC, e.likes DESC, e.published_date DESC
      LIMIT ?
    `
    
    return executeQuery<Experience>(query, [limit])
  }
}
