import { Button } from '@/components/ui/button'

interface Category {
  id: number | string
  name: string
  count?: number
}

interface CategoryFilterProps {
  categories: Category[]
  selectedCategory: number | string | null
  onCategoryChange: (categoryId: number | string | null) => void
  showAll?: boolean
  className?: string
}

export default function CategoryFilter({ 
  categories, 
  selectedCategory, 
  onCategoryChange,
  showAll = true,
  className = ""
}: CategoryFilterProps) {
  const allCategories = showAll 
    ? [{ id: 'all', name: 'Tất cả', count: categories.reduce((sum, cat) => sum + (cat.count || 0), 0) }, ...categories]
    : categories

  return (
    <div className={`flex flex-wrap gap-4 justify-center ${className}`}>
      {allCategories.map((category) => (
        <Button
          key={category.id}
          variant={selectedCategory === category.id ? 'default' : 'outline'}
          onClick={() => onCategoryChange(category.id === 'all' ? null : category.id)}
          className={`${
            selectedCategory === category.id 
              ? 'bg-[#3B82F6] hover:bg-[#2563EB] text-white' 
              : 'border-[#3B82F6] text-[#3B82F6] hover:bg-[#3B82F6] hover:text-white'
          }`}
        >
          {category.name}
          {category.count !== undefined && (
            <span className="ml-2">({category.count})</span>
          )}
        </Button>
      ))}
    </div>
  )
}
