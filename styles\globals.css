@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    /* Interior Design Theme - Warm & Professional */
    --background: 35 20% 97%; /* Soft cream background */
    --foreground: 30 15% 15%; /* Deep brown text */
    --card: 35 25% 98%; /* Warm white cards */
    --card-foreground: 30 15% 15%;
    --popover: 35 25% 98%;
    --popover-foreground: 30 15% 15%;
    --primary: 30 35% 35%; /* Warm brown primary */
    --primary-foreground: 35 25% 98%;
    --secondary: 35 15% 92%; /* Warm beige secondary */
    --secondary-foreground: 30 25% 25%;
    --muted: 35 12% 94%; /* Light warm gray */
    --muted-foreground: 30 8% 50%;
    --accent: 85 25% 65%; /* Sage green accent */
    --accent-foreground: 30 15% 15%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 35 15% 88%; /* Warm border */
    --input: 35 15% 90%; /* Warm input background */
    --ring: 30 35% 35%; /* Warm brown focus ring */
    --chart-1: 85 25% 65%; /* Sage green */
    --chart-2: 30 35% 45%; /* Warm brown */
    --chart-3: 25 60% 55%; /* Terracotta */
    --chart-4: 45 75% 55%; /* Warm gold */
    --chart-5: 30 45% 50%; /* Muted copper */
    --radius: 0.5rem;

    /* Enhanced spacing system */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* Enhanced shadow system */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

    /* Enhanced border radius system */
    --radius-xs: 0.125rem;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;

    /* Animation timing */
    --duration-fast: 0.15s;
    --duration-normal: 0.25s;
    --duration-slow: 0.35s;
    --ease-in-out: cubic-bezier(0.16, 1, 0.3, 1);
    --ease-spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Admin Sidebar - Professional Interior Theme */
    --sidebar-background: 30 20% 96%; /* Warm cream sidebar */
    --sidebar-foreground: 30 25% 25%;
    --sidebar-primary: 30 35% 35%; /* Warm brown */
    --sidebar-primary-foreground: 35 25% 98%;
    --sidebar-accent: 35 15% 92%; /* Warm beige */
    --sidebar-accent-foreground: 30 25% 25%;
    --sidebar-border: 35 15% 85%;
    --sidebar-ring: 30 35% 35%;
  }
  .dark {
    /* Dark Interior Design Theme */
    --background: 30 15% 8%; /* Deep charcoal background */
    --foreground: 35 20% 95%; /* Warm cream text */
    --card: 30 15% 10%; /* Dark brown cards */
    --card-foreground: 35 20% 95%;
    --popover: 30 15% 10%;
    --popover-foreground: 35 20% 95%;
    --primary: 35 20% 85%; /* Light warm cream primary */
    --primary-foreground: 30 15% 15%;
    --secondary: 30 10% 18%; /* Dark warm gray */
    --secondary-foreground: 35 20% 90%;
    --muted: 30 8% 15%; /* Dark muted */
    --muted-foreground: 35 10% 65%;
    --accent: 85 20% 45%; /* Darker sage green */
    --accent-foreground: 35 20% 95%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 30 10% 20%; /* Dark warm border */
    --input: 30 10% 18%; /* Dark input */
    --ring: 35 20% 85%; /* Light focus ring */
    --chart-1: 85 20% 45%; /* Dark sage green */
    --chart-2: 30 25% 55%; /* Medium brown */
    --chart-3: 25 50% 45%; /* Dark terracotta */
    --chart-4: 45 65% 45%; /* Dark gold */
    --chart-5: 30 35% 40%; /* Dark copper */

    /* Enhanced spacing system (same for dark) */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* Enhanced shadow system for dark theme */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.3);

    /* Enhanced border radius system (same for dark) */
    --radius-xs: 0.125rem;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;

    /* Animation timing (same for dark) */
    --duration-fast: 0.15s;
    --duration-normal: 0.25s;
    --duration-slow: 0.35s;
    --ease-in-out: cubic-bezier(0.16, 1, 0.3, 1);
    --ease-spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Dark Sidebar */
    --sidebar-background: 30 15% 12%; /* Dark brown sidebar */
    --sidebar-foreground: 35 15% 85%;
    --sidebar-primary: 35 20% 85%; /* Light cream */
    --sidebar-primary-foreground: 30 15% 15%;
    --sidebar-accent: 30 10% 18%; /* Dark accent */
    --sidebar-accent-foreground: 35 15% 85%;
    --sidebar-border: 30 8% 22%;
    --sidebar-ring: 35 20% 85%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
