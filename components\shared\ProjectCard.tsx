import Image from 'next/image'
import Link from 'next/link'
import { Badge } from '@/components/ui/badge'
import { MapPin, Calendar, Ruler } from 'lucide-react'

interface Project {
  id: number
  title: string
  slug: string
  description?: string
  image?: string
  area?: string
  location?: string
  completed_date?: string
  category_name?: string
  featured?: boolean
}

interface ProjectCardProps {
  project: Project
  showCategory?: boolean
  showLocation?: boolean
  showArea?: boolean
  className?: string
}

export default function ProjectCard({ 
  project, 
  showCategory = true,
  showLocation = true,
  showArea = true,
  className = ""
}: ProjectCardProps) {
  const defaultImage = "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600"

  return (
    <div className={`bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 ${className}`}>
      <div className="relative h-64">
        <Image
          src={project.image || defaultImage}
          alt={project.title}
          fill
          className="object-cover"
        />
        {project.featured && (
          <div className="absolute top-4 left-4">
            <Badge className="bg-red-500 text-white">
              Nổi bật
            </Badge>
          </div>
        )}
        {showCategory && project.category_name && (
          <div className="absolute top-4 right-4">
            <Badge className="bg-blue-500 text-white">
              {project.category_name}
            </Badge>
          </div>
        )}
      </div>
      
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
          <Link 
            href={`/du-an-da-hoan-thien/${project.slug}`}
            className="hover:text-blue-600 transition-colors"
          >
            {project.title}
          </Link>
        </h3>
        
        {project.description && (
          <p className="text-gray-600 mb-4 line-clamp-3">
            {project.description}
          </p>
        )}
        
        <div className="space-y-2 text-sm text-gray-500">
          {showLocation && project.location && (
            <div className="flex items-center">
              <MapPin className="h-4 w-4 mr-2" />
              {project.location}
            </div>
          )}
          
          {showArea && project.area && (
            <div className="flex items-center">
              <Ruler className="h-4 w-4 mr-2" />
              {project.area}
            </div>
          )}
          
          {project.completed_date && (
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-2" />
              Hoàn thành: {new Date(project.completed_date).toLocaleDateString('vi-VN')}
            </div>
          )}
        </div>
        
        <div className="mt-4">
          <Link 
            href={`/du-an-da-hoan-thien/${project.slug}`}
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            Xem chi tiết →
          </Link>
        </div>
      </div>
    </div>
  )
}
