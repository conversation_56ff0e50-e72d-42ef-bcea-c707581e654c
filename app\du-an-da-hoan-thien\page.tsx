import { Metadata } from "next"
import Header from "@/components/Header"
import Footer from "@/components/Footer"
import FloatingActionBar from "@/components/FloatingActionBar"
import Image from "next/image"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export const metadata: Metadata = {
  title: "Dự Án Đã Hoàn <PERSON>hiện - <PERSON>ến <PERSON>",
  description: "Khám phá các dự án nội thất đã hoàn thiện của Kiến Trú<PERSON>ư Thái Vũ",
}

// Temporary data - sẽ thay bằng API call
const categories = [
  { id: 'all', name: '<PERSON><PERSON><PERSON>', count: 24 },
  { id: 'nha-pho', name: '<PERSON><PERSON><PERSON>ố', count: 8 },
  { id: 'chung-cu', name: '<PERSON>ư', count: 6 },
  { id: 'biet-thu', name: '<PERSON><PERSON><PERSON><PERSON>ự', count: 5 },
  { id: 'van-phong', name: '<PERSON><PERSON><PERSON>', count: 3 },
  { id: 'showroom', name: 'Showroom', count: 2 }
]

const projects = [
  {
    id: 1,
    title: "<PERSON><PERSON>ệ<PERSON> Thự <PERSON>ện Đại Quận 7",
    category: "biet-thu",
    area: "350m²",
    location: "TP. Hồ Chí Minh",
    completedDate: "2024-01-15",
    image: "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
    images: [
      "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
      "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600"
    ],
    description: "Thiết kế nội thất biệt thự hiện đại với phong cách tối giản, tận dụng tối đa ánh sáng tự nhiên."
  },
  {
    id: 2,
    title: "Chung Cư Cao Cấp Landmark 81",
    category: "chung-cu",
    area: "120m²",
    location: "TP. Hồ Chí Minh",
    completedDate: "2023-12-20",
    image: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
    images: [
      "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600"
    ],
    description: "Căn hộ cao cấp với thiết kế sang trọng, tối ưu hóa không gian sống."
  },
  {
    id: 3,
    title: "Nhà Phố Phong Cách Nhật Bản",
    category: "nha-pho",
    area: "180m²",
    location: "Buôn Ma Thuột",
    completedDate: "2023-11-10",
    image: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
    images: [
      "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600"
    ],
    description: "Nhà phố với phong cách Nhật Bản, sử dụng vật liệu gỗ tự nhiên và thiết kế zen."
  },
  {
    id: 4,
    title: "Văn Phòng Công Ty Công Nghệ",
    category: "van-phong",
    area: "500m²",
    location: "TP. Hồ Chí Minh",
    completedDate: "2023-10-05",
    image: "https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
    images: [
      "https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600"
    ],
    description: "Không gian văn phòng hiện đại, thúc đẩy sự sáng tạo và làm việc nhóm."
  }
]

export default function DuAnDaHoanThienPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Banner */}
      <section className="relative h-[400px] mt-[120px]">
        <div
          className="w-full h-full bg-cover bg-center"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=800')`
          }}
        >
          <div className="absolute inset-0 bg-black bg-opacity-50" />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-white">
              <h1 className="text-5xl md:text-6xl font-bold mb-4">DỰ ÁN ĐÃ HOÀN THIỆN</h1>
              <p className="text-xl md:text-2xl">Khám phá những công trình nội thất đẳng cấp</p>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Filter */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap gap-4 justify-center">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={category.id === 'all' ? 'default' : 'outline'}
                className={`${
                  category.id === 'all' 
                    ? 'bg-[#3B82F6] hover:bg-[#2563EB] text-white' 
                    : 'border-[#3B82F6] text-[#3B82F6] hover:bg-[#3B82F6] hover:text-white'
                }`}
              >
                {category.name} ({category.count})
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {projects.map((project) => (
              <div key={project.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div className="relative h-64">
                  <Image
                    src={project.image}
                    alt={project.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute top-4 left-4">
                    <Badge className="bg-[#3B82F6] text-white">
                      {categories.find(cat => cat.id === project.category)?.name}
                    </Badge>
                  </div>
                </div>

                <div className="p-6">
                  <h3 className="text-xl font-bold text-[#1F2937] mb-3">{project.title}</h3>
                  <p className="text-[#6B7280] mb-4 line-clamp-2">{project.description}</p>

                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-[#6B7280]">Diện tích:</span>
                      <span className="font-medium text-[#1F2937]">{project.area}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-[#6B7280]">Địa điểm:</span>
                      <span className="font-medium text-[#1F2937]">{project.location}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-[#6B7280]">Hoàn thành:</span>
                      <span className="font-medium text-[#1F2937]">
                        {new Date(project.completedDate).toLocaleDateString('vi-VN')}
                      </span>
                    </div>
                  </div>

                  <Link href={`/du-an/${project.id}`}>
                    <Button className="w-full bg-[#3B82F6] hover:bg-[#2563EB] text-white">
                      Xem Chi Tiết
                    </Button>
                  </Link>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination */}
          <div className="flex justify-center mt-12">
            <div className="flex gap-2">
              <Button variant="outline" disabled>Trước</Button>
              <Button className="bg-[#3B82F6] text-white">1</Button>
              <Button variant="outline">2</Button>
              <Button variant="outline">3</Button>
              <Button variant="outline">Sau</Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
      <FloatingActionBar />
    </div>
  )
}
