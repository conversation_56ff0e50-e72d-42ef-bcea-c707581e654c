import { Metadata } from "next"
import Header from "@/components/Header"
import Footer from "@/components/Footer"
import FloatingActionBar from "@/components/FloatingActionBar"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar, User, Eye, Heart } from "lucide-react"

export const metadata: Metadata = {
  title: "Chia Sẻ Kinh Nghiệm - Kiến Trúc <PERSON>",
  description: "Chia sẻ kinh nghiệm thiết kế và thi công nội thất từ các chuyên gia",
}

// Temporary data - sẽ thay bằng API call
const categories = [
  { id: 'all', name: '<PERSON><PERSON><PERSON>', count: 32 },
  { id: 'thiet-ke', name: '<PERSON><PERSON><PERSON><PERSON>', count: 12 },
  { id: 'thi-cong', name: '<PERSON>hi Công', count: 8 },
  { id: 'quan-ly', name: '<PERSON><PERSON><PERSON><PERSON> <PERSON> Án', count: 6 },
  { id: 'khach-hang', name: '<PERSON><PERSON><PERSON><PERSON>', count: 6 }
]

const experiences = [
  {
    id: 1,
    title: "<PERSON>nh <PERSON>ệ<PERSON>hiết Kế Cho Gia Đình Trẻ Với Ngân Sách Hạn Chế",
    slug: "thiet-ke-gia-dinh-tre-ngan-sach-han-che",
    category: "thiet-ke",
    excerpt: "Chia sẻ cách tối ưu hóa thiết kế nội thất cho gia đình trẻ với ngân sách từ 200-500 triệu đồng.",
    content: "Nội dung chi tiết về kinh nghiệm thiết kế...",
    image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
    author: "Kiến Trúc Sư Thái Vũ",
    authorAvatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100",
    publishedDate: "2024-01-15",
    views: 2150,
    likes: 89,
    featured: true,
    readTime: "8 phút đọc"
  },
  {
    id: 2,
    title: "Những Sai Lầm Thường Gặp Khi Thi Công Nội Thất",
    slug: "sai-lam-thuong-gap-khi-thi-cong",
    category: "thi-cong",
    excerpt: "15 năm kinh nghiệm thi công, tôi chia sẻ những sai lầm phổ biến và cách tránh.",
    content: "Nội dung chi tiết về sai lầm thi công...",
    image: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
    author: "Foreman Minh Tuấn",
    authorAvatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100",
    publishedDate: "2024-01-10",
    views: 1890,
    likes: 67,
    featured: true,
    readTime: "12 phút đọc"
  },
  {
    id: 3,
    title: "Cách Quản Lý Timeline Dự Án Nội Thất Hiệu Quả",
    slug: "quan-ly-timeline-du-an-hieu-qua",
    category: "quan-ly",
    excerpt: "Phương pháp quản lý tiến độ dự án để đảm bảo hoàn thành đúng hạn và chất lượng.",
    content: "Nội dung chi tiết về quản lý timeline...",
    image: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
    author: "Project Manager Lan Anh",
    authorAvatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100",
    publishedDate: "2024-01-05",
    views: 1456,
    likes: 45,
    featured: false,
    readTime: "10 phút đọc"
  },
  {
    id: 4,
    title: "Làm Thế Nào Để Khách Hàng Luôn Hài Lòng Với Dự Án",
    slug: "khach-hang-hai-long-voi-du-an",
    category: "khach-hang",
    excerpt: "Bí quyết giao tiếp và quản lý kỳ vọng khách hàng trong suốt quá trình thực hiện dự án.",
    content: "Nội dung chi tiết về tương tác khách hàng...",
    image: "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
    author: "Sales Manager Hoàng Nam",
    authorAvatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100",
    publishedDate: "2023-12-28",
    views: 1234,
    likes: 56,
    featured: false,
    readTime: "6 phút đọc"
  },
  {
    id: 5,
    title: "Xu Hướng Vật Liệu Nội Thất 2024: Góc Nhìn Từ Thực Tế",
    slug: "xu-huong-vat-lieu-noi-that-2024",
    category: "thiet-ke",
    excerpt: "Phân tích xu hướng vật liệu mới dựa trên kinh nghiệm thực tế từ các dự án đã triển khai.",
    content: "Nội dung chi tiết về xu hướng vật liệu...",
    image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600",
    author: "Kiến Trúc Sư Thái Vũ",
    authorAvatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100",
    publishedDate: "2023-12-20",
    views: 1678,
    likes: 78,
    featured: false,
    readTime: "15 phút đọc"
  }
]

const featuredExperiences = experiences.filter(exp => exp.featured)
const regularExperiences = experiences.filter(exp => !exp.featured)

export default function ChiaSeKinhNghiemPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Banner */}
      <section className="relative h-[400px] mt-[120px]">
        <div
          className="w-full h-full bg-cover bg-center"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=800')`
          }}
        >
          <div className="absolute inset-0 bg-black bg-opacity-50" />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-white">
              <h1 className="text-5xl md:text-6xl font-bold mb-4">CHIA SẺ KINH NGHIỆM</h1>
              <p className="text-xl md:text-2xl">Kinh nghiệm thực tế từ các chuyên gia</p>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Filter */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap gap-4 justify-center">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={category.id === 'all' ? 'default' : 'outline'}
                className={`${
                  category.id === 'all' 
                    ? 'bg-[#3B82F6] hover:bg-[#2563EB] text-white' 
                    : 'border-[#3B82F6] text-[#3B82F6] hover:bg-[#3B82F6] hover:text-white'
                }`}
              >
                {category.name} ({category.count})
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Experiences */}
      {featuredExperiences.length > 0 && (
        <section className="py-12 bg-gray-50">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-[#1F2937] mb-8 text-center">KINH NGHIỆM NỔI BẬT</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {featuredExperiences.map((experience) => (
                <div key={experience.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                  <div className="relative h-64">
                    <Image
                      src={experience.image}
                      alt={experience.title}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute top-4 left-4">
                      <Badge className="bg-red-500 text-white">NỔI BẬT</Badge>
                    </div>
                    <div className="absolute top-4 right-4">
                      <Badge className="bg-black bg-opacity-70 text-white">
                        {experience.readTime}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="p-6">
                    <div className="flex items-center gap-2 mb-3">
                      <Badge variant="outline" className="border-[#3B82F6] text-[#3B82F6]">
                        {categories.find(cat => cat.id === experience.category)?.name}
                      </Badge>
                    </div>
                    
                    <h3 className="text-xl font-bold text-[#1F2937] mb-3 line-clamp-2">{experience.title}</h3>
                    <p className="text-[#6B7280] mb-4 line-clamp-3">{experience.excerpt}</p>
                    
                    <div className="flex items-center gap-4 mb-4">
                      <div className="flex items-center gap-2">
                        <div className="relative w-8 h-8 rounded-full overflow-hidden">
                          <Image
                            src={experience.authorAvatar}
                            alt={experience.author}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <span className="text-sm font-medium text-[#1F2937]">{experience.author}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm text-[#6B7280] mb-4">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <Calendar className="w-4 h-4" />
                          <span>{new Date(experience.publishedDate).toLocaleDateString('vi-VN')}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Eye className="w-4 h-4" />
                          <span>{experience.views}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        <Heart className="w-4 h-4" />
                        <span>{experience.likes}</span>
                      </div>
                    </div>
                    
                    <Button className="w-full bg-[#3B82F6] hover:bg-[#2563EB] text-white">
                      Đọc Kinh Nghiệm
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Regular Experiences */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-[#1F2937] mb-12 text-center">TẤT CẢ KINH NGHIỆM</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {regularExperiences.map((experience) => (
              <div key={experience.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div className="relative h-48">
                  <Image
                    src={experience.image}
                    alt={experience.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute top-4 right-4">
                    <Badge className="bg-black bg-opacity-70 text-white text-xs">
                      {experience.readTime}
                    </Badge>
                  </div>
                </div>
                
                <div className="p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <Badge variant="outline" className="border-[#3B82F6] text-[#3B82F6] text-xs">
                      {categories.find(cat => cat.id === experience.category)?.name}
                    </Badge>
                  </div>
                  
                  <h3 className="text-lg font-bold text-[#1F2937] mb-3 line-clamp-2">{experience.title}</h3>
                  <p className="text-[#6B7280] mb-4 line-clamp-2">{experience.excerpt}</p>
                  
                  <div className="flex items-center gap-2 mb-4">
                    <div className="relative w-6 h-6 rounded-full overflow-hidden">
                      <Image
                        src={experience.authorAvatar}
                        alt={experience.author}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <span className="text-sm text-[#6B7280]">{experience.author}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-[#6B7280] mb-4">
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      <span>{new Date(experience.publishedDate).toLocaleDateString('vi-VN')}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-1">
                        <Eye className="w-4 h-4" />
                        <span>{experience.views}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Heart className="w-4 h-4" />
                        <span>{experience.likes}</span>
                      </div>
                    </div>
                  </div>
                  
                  <Button className="w-full bg-[#3B82F6] hover:bg-[#2563EB] text-white">
                    Đọc Kinh Nghiệm
                  </Button>
                </div>
              </div>
            ))}
          </div>
          
          {/* Pagination */}
          <div className="flex justify-center mt-12">
            <div className="flex gap-2">
              <Button variant="outline" disabled>Trước</Button>
              <Button className="bg-[#3B82F6] text-white">1</Button>
              <Button variant="outline">2</Button>
              <Button variant="outline">3</Button>
              <Button variant="outline">Sau</Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
      <FloatingActionBar />
    </div>
  )
}
