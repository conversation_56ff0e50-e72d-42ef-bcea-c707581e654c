import { NextRequest, NextResponse } from 'next/server'
import { ArticleModel } from '@/lib/models/article'

// GET /api/articles
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category_id = searchParams.get('category_id')
    const featured = searchParams.get('featured')
    const status = searchParams.get('status')
    const limit = searchParams.get('limit')
    const offset = searchParams.get('offset')
    const search = searchParams.get('search')
    const popular = searchParams.get('popular')

    // Handle search
    if (search) {
      const articles = await ArticleModel.search(search, limit ? parseInt(limit) : 10)
      return NextResponse.json({
        success: true,
        data: articles
      })
    }

    // Handle popular articles
    if (popular === 'true') {
      const articles = await ArticleModel.getPopular(limit ? parseInt(limit) : 10)
      return NextResponse.json({
        success: true,
        data: articles
      })
    }

    // Handle regular filtering
    const filters: any = {}
    
    if (category_id) filters.category_id = parseInt(category_id)
    if (featured !== null) filters.featured = featured === 'true'
    if (status) filters.status = status
    if (limit) filters.limit = parseInt(limit)
    if (offset) filters.offset = parseInt(offset)

    const articles = await ArticleModel.getAll(filters)

    return NextResponse.json({
      success: true,
      data: articles
    })
  } catch (error) {
    console.error('Articles API Error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch articles'
      },
      { status: 500 }
    )
  }
}

// POST /api/articles
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      title,
      slug,
      excerpt,
      content,
      image,
      author,
      category_id,
      featured,
      views,
      status,
      published_date
    } = body

    if (!title) {
      return NextResponse.json(
        {
          success: false,
          error: 'Title is required'
        },
        { status: 400 }
      )
    }

    const articleId = await ArticleModel.create({
      title,
      slug,
      excerpt,
      content,
      image,
      author,
      category_id,
      featured,
      views,
      status,
      published_date
    })

    const article = await ArticleModel.getById(articleId)

    return NextResponse.json({
      success: true,
      data: article
    }, { status: 201 })
  } catch (error) {
    console.error('Articles API Error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create article'
      },
      { status: 500 }
    )
  }
}
