import { Metadata } from "next"
import Header from "@/components/Header"
import Footer from "@/components/Footer"
import FloatingActionBar from "@/components/FloatingActionBar"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Award, Users, Building, Target, Heart, Star } from "lucide-react"

export const metadata: Metadata = {
  title: "Giới Thiệu - Kiến Trúc <PERSON>ư Thá<PERSON> Vũ",
  description: "Tìm hiểu về Kiến Trúc Sư Thái Vũ - Chuyên gia thiết kế nội thất hàng đầu",
}

// Temporary data - sẽ thay bằng API call
const achievements = [
  { icon: Building, number: "500+", label: "Dự Án Hoàn <PERSON>h<PERSON>" },
  { icon: Users, number: "15", label: "Nă<PERSON>" },
  { icon: Award, number: "50+", label: "Giải Thưởng" },
  { icon: Star, number: "98%", label: "<PERSON><PERSON><PERSON>ch <PERSON>àng <PERSON>" }
]

const teamMembers = [
  {
    name: "<PERSON>ến Trúc Sư Thái Vũ",
    position: "Founder & Lead Designer",
    experience: "15 năm kinh nghiệm",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=400",
    description: "Tốt nghiệp Đại học Kiến trúc TP.HCM, chuyên về thiết kế nội thất hiện đại và luxury."
  },
  {
    name: "KTS. Minh Tuấn",
    position: "Senior Designer",
    experience: "10 năm kinh nghiệm",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=400",
    description: "Chuyên gia thiết kế không gian thương mại và văn phòng hiện đại."
  },
  {
    name: "KTS. Lan Anh",
    position: "Interior Designer",
    experience: "8 năm kinh nghiệm",
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=400",
    description: "Chuyên về thiết kế nội thất gia đình và phong cách Scandinavian."
  }
]

const services = [
  {
    title: "Thiết Kế Nội Thất",
    description: "Thiết kế 2D, 3D chi tiết cho mọi không gian",
    features: ["Thiết kế concept", "Bản vẽ thi công", "3D Rendering", "Tư vấn phong thủy"]
  },
  {
    title: "Thi Công Trọn Gói",
    description: "Thi công từ A-Z với đội ngũ thợ lành nghề",
    features: ["Quản lý dự án", "Thi công chuyên nghiệp", "Giám sát chất lượng", "Bảo hành 5 năm"]
  },
  {
    title: "Tư Vấn Chuyên Sâu",
    description: "Tư vấn giải pháp tối ưu cho từng không gian",
    features: ["Phân tích không gian", "Tối ưu ngân sách", "Lựa chọn vật liệu", "Hỗ trợ 24/7"]
  }
]

export default function GioiThieuPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Banner */}
      <section className="relative h-[500px] mt-[120px]">
        <div
          className="w-full h-full bg-cover bg-center"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=800')`
          }}
        >
          <div className="absolute inset-0 bg-black bg-opacity-50" />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-white max-w-4xl px-4">
              <h1 className="text-5xl md:text-6xl font-bold mb-6">KIẾN TRÚC SƯ THÁI VŨ</h1>
              <p className="text-xl md:text-2xl mb-8">
                Chuyên gia thiết kế nội thất với 15 năm kinh nghiệm
              </p>
              <p className="text-lg md:text-xl max-w-3xl mx-auto">
                Chúng tôi tạo ra những không gian sống đẳng cấp, phản ánh cá tính và phong cách riêng của từng gia đình
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Achievements */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {achievements.map((achievement, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-[#3B82F6] rounded-full flex items-center justify-center mx-auto mb-4">
                  <achievement.icon className="w-8 h-8 text-white" />
                </div>
                <div className="text-3xl font-bold text-[#1F2937] mb-2">{achievement.number}</div>
                <div className="text-[#6B7280]">{achievement.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Tabs Content */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <Tabs defaultValue="about" className="w-full">
            <TabsList className="grid w-full grid-cols-4 mb-12">
              <TabsTrigger value="about">Về Chúng Tôi</TabsTrigger>
              <TabsTrigger value="team">Đội Ngũ</TabsTrigger>
              <TabsTrigger value="services">Dịch Vụ</TabsTrigger>
              <TabsTrigger value="mission">Sứ Mệnh</TabsTrigger>
            </TabsList>
            
            <TabsContent value="about" className="space-y-12">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h2 className="text-4xl font-bold text-[#1F2937] mb-6">Câu Chuyện Của Chúng Tôi</h2>
                  <div className="space-y-4 text-[#6B7280] leading-relaxed">
                    <p>
                      Được thành lập từ năm 2009, Kiến Trúc Sư Thái Vũ đã trở thành một trong những thương hiệu 
                      thiết kế nội thất uy tín hàng đầu tại Việt Nam. Với hơn 15 năm kinh nghiệm trong ngành, 
                      chúng tôi đã hoàn thành hơn 500 dự án từ nhà ở cá nhân đến các công trình thương mại lớn.
                    </p>
                    <p>
                      Triết lý thiết kế của chúng tôi là tạo ra những không gian sống không chỉ đẹp mắt mà còn 
                      phản ánh đúng cá tính, phong cách sống và nhu cầu thực tế của từng gia đình. Mỗi dự án 
                      đều được chúng tôi đầu tư tâm huyết để mang đến giải pháp tối ưu nhất.
                    </p>
                    <p>
                      Từ Buôn Ma Thuột đến TP. Hồ Chí Minh, chúng tôi đã để lại dấu ấn qua những công trình 
                      chất lượng cao, được khách hàng tin tưởng và đánh giá cao về tính chuyên nghiệp.
                    </p>
                  </div>
                </div>
                <div className="relative h-[400px] rounded-lg overflow-hidden shadow-lg">
                  <Image
                    src="https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600"
                    alt="Về chúng tôi"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="team" className="space-y-12">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold text-[#1F2937] mb-4">Đội Ngũ Chuyên Gia</h2>
                <p className="text-xl text-[#6B7280] max-w-3xl mx-auto">
                  Đội ngũ kiến trúc sư và designer giàu kinh nghiệm, luôn cập nhật xu hướng thiết kế mới nhất
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {teamMembers.map((member, index) => (
                  <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    <div className="relative h-64">
                      <Image
                        src={member.image}
                        alt={member.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-bold text-[#1F2937] mb-2">{member.name}</h3>
                      <p className="text-[#3B82F6] font-medium mb-2">{member.position}</p>
                      <p className="text-[#6B7280] text-sm mb-3">{member.experience}</p>
                      <p className="text-[#6B7280] text-sm leading-relaxed">{member.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="services" className="space-y-12">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold text-[#1F2937] mb-4">Dịch Vụ Của Chúng Tôi</h2>
                <p className="text-xl text-[#6B7280] max-w-3xl mx-auto">
                  Giải pháp thiết kế và thi công nội thất toàn diện từ A đến Z
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {services.map((service, index) => (
                  <div key={index} className="bg-white rounded-lg shadow-lg p-8 hover:shadow-xl transition-shadow duration-300">
                    <h3 className="text-2xl font-bold text-[#1F2937] mb-4">{service.title}</h3>
                    <p className="text-[#6B7280] mb-6">{service.description}</p>
                    <ul className="space-y-3">
                      {service.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center gap-3">
                          <div className="w-2 h-2 bg-[#3B82F6] rounded-full"></div>
                          <span className="text-[#1F2937]">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="mission" className="space-y-12">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div className="relative h-[400px] rounded-lg overflow-hidden shadow-lg">
                  <Image
                    src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600"
                    alt="Sứ mệnh"
                    fill
                    className="object-cover"
                  />
                </div>
                <div>
                  <h2 className="text-4xl font-bold text-[#1F2937] mb-6">Sứ Mệnh & Tầm Nhìn</h2>
                  
                  <div className="space-y-6">
                    <div className="flex gap-4">
                      <div className="w-12 h-12 bg-[#3B82F6] rounded-full flex items-center justify-center flex-shrink-0">
                        <Target className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-[#1F2937] mb-2">Sứ Mệnh</h3>
                        <p className="text-[#6B7280] leading-relaxed">
                          Tạo ra những không gian sống đẳng cấp, mang lại hạnh phúc và cảm hứng cho mọi gia đình Việt Nam. 
                          Chúng tôi cam kết mang đến dịch vụ chất lượng cao với giá trị tốt nhất.
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex gap-4">
                      <div className="w-12 h-12 bg-[#3B82F6] rounded-full flex items-center justify-center flex-shrink-0">
                        <Heart className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-[#1F2937] mb-2">Tầm Nhìn</h3>
                        <p className="text-[#6B7280] leading-relaxed">
                          Trở thành thương hiệu thiết kế nội thất hàng đầu Việt Nam, được khách hàng tin tưởng 
                          và đối tác quốc tế công nhận về chất lượng dịch vụ và tính sáng tạo.
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex gap-4">
                      <div className="w-12 h-12 bg-[#3B82F6] rounded-full flex items-center justify-center flex-shrink-0">
                        <Award className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-[#1F2937] mb-2">Giá Trị Cốt Lõi</h3>
                        <p className="text-[#6B7280] leading-relaxed">
                          Chất lượng - Sáng tạo - Uy tín - Tận tâm. Chúng tôi luôn đặt khách hàng làm trung tâm, 
                          không ngừng học hỏi và cải tiến để mang đến những giải pháp tối ưu nhất.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-[#1F2937] text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-6">Sẵn Sàng Bắt Đầu Dự Án Của Bạn?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Liên hệ ngay với chúng tôi để được tư vấn miễn phí và nhận báo giá chi tiết
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button className="bg-[#3B82F6] hover:bg-[#2563EB] text-white px-8 py-4 text-lg">
              Tư Vấn Miễn Phí
            </Button>
            <Button variant="outline" className="border-white text-white hover:bg-white hover:text-[#1F2937] px-8 py-4 text-lg">
              Xem Portfolio
            </Button>
          </div>
        </div>
      </section>

      <Footer />
      <FloatingActionBar />
    </div>
  )
}
