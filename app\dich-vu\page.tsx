"use client"

import { useState, useEffect } from "react"
import Head<PERSON> from "@/components/Header"
import Footer from "@/components/Footer"
import FloatingActionBar from "@/components/FloatingActionBar"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Check, Star, ArrowRight, Loader2 } from "lucide-react"
import { apiClient } from "@/lib/api-client"
import CategoryFilter from "@/components/shared/CategoryFilter"
import SearchBox from "@/components/shared/SearchBox"
import Pagination from "@/components/shared/Pagination"

interface Service {
  id: number
  title: string
  slug: string
  description?: string
  content?: string
  image?: string
  features?: string[]
  price_range?: string
  duration?: string
  category_name?: string
  featured?: boolean
}

interface Category {
  id: number
  name: string
  count: number
}

export default function DichVuPage() {
  const [services, setServices] = useState<Service[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const itemsPerPage = 6

  useEffect(() => {
    fetchData()
  }, [selectedCategory, searchQuery, currentPage])

  const fetchData = async () => {
    setLoading(true)
    try {
      // Fetch categories
      const categoriesResponse = await apiClient.getCategories('services')
      if (categoriesResponse.success) {
        setCategories(categoriesResponse.data || [])
      }

      // Fetch services
      const params: any = {
        limit: itemsPerPage,
        offset: (currentPage - 1) * itemsPerPage
      }
      
      if (selectedCategory) {
        params.category_id = selectedCategory
      }
      
      if (searchQuery) {
        params.search = searchQuery
      }

      const servicesResponse = await apiClient.getServices(params)
      if (servicesResponse.success) {
        setServices(servicesResponse.data || [])
        setTotalPages(servicesResponse.pagination?.totalPages || 1)
      }
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCategoryChange = (categoryId: number | null) => {
    setSelectedCategory(categoryId)
    setCurrentPage(1)
  }

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Banner */}
      <section className="relative h-[400px] mt-[120px]">
        <div
          className="w-full h-full bg-cover bg-center"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=800')`
          }}
        >
          <div className="absolute inset-0 bg-black bg-opacity-50" />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-white">
              <h1 className="text-5xl md:text-6xl font-bold mb-4">DỊCH VỤ</h1>
              <p className="text-xl md:text-2xl">Giải pháp nội thất toàn diện cho mọi không gian</p>
            </div>
          </div>
        </div>
      </section>

      {/* Search Section */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            <SearchBox
              placeholder="Tìm kiếm dịch vụ..."
              onSearch={handleSearch}
              className="w-full"
            />
          </div>
        </div>
      </section>

      {/* Categories Filter */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <CategoryFilter
            categories={categories}
            selectedCategory={selectedCategory}
            onCategoryChange={handleCategoryChange}
          />
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {services.map((service) => (
                  <div key={service.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    <div className="relative h-64">
                      <Image
                        src={service.image || "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600"}
                        alt={service.title}
                        fill
                        className="object-cover"
                      />
                      {service.featured && (
                        <div className="absolute top-4 left-4">
                          <Badge className="bg-red-500 text-white">
                            Nổi bật
                          </Badge>
                        </div>
                      )}
                      {service.category_name && (
                        <div className="absolute top-4 right-4">
                          <Badge className="bg-blue-500 text-white">
                            {service.category_name}
                          </Badge>
                        </div>
                      )}
                    </div>
                    
                    <div className="p-6">
                      <h3 className="text-xl font-bold text-gray-900 mb-3">
                        {service.title}
                      </h3>
                      
                      {service.description && (
                        <p className="text-gray-600 mb-4">
                          {service.description}
                        </p>
                      )}
                      
                      {service.features && service.features.length > 0 && (
                        <ul className="space-y-2 mb-6">
                          {service.features.slice(0, 3).map((feature, index) => (
                            <li key={index} className="flex items-center gap-2">
                              <Check className="h-4 w-4 text-green-500" />
                              <span className="text-sm text-gray-700">{feature}</span>
                            </li>
                          ))}
                        </ul>
                      )}
                      
                      <div className="flex items-center justify-between">
                        {service.price_range && (
                          <div className="text-lg font-bold text-blue-600">
                            {service.price_range}
                          </div>
                        )}
                        
                        <Button className="bg-[#3B82F6] hover:bg-[#2563EB]">
                          Xem chi tiết
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="mt-12">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                  />
                </div>
              )}

              {services.length === 0 && !loading && (
                <div className="text-center py-12">
                  <p className="text-gray-500 text-lg">Không tìm thấy dịch vụ nào.</p>
                </div>
              )}
            </>
          )}
        </div>
      </section>

      <Footer />
      <FloatingActionBar />
    </div>
  )
}
