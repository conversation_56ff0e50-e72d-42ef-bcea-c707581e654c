"use client"

import { useState, useEffect, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import Header from "@/components/Header"
import Footer from "@/components/Footer"
import FloatingActionBar from "@/components/FloatingActionBar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Loader2, Search } from "lucide-react"
import ArticleCard from "@/components/shared/ArticleCard"
import ProjectCard from "@/components/shared/ProjectCard"
import VideoCard from "@/components/shared/VideoCard"
import SearchBox from "@/components/shared/SearchBox"

interface SearchResults {
  services?: any[]
  projects?: any[]
  articles?: any[]
  videos?: any[]
  experiences?: any[]
  total?: number
}

function SearchPageContent() {
  const searchParams = useSearchParams()
  const [results, setResults] = useState<SearchResults>({})
  const [loading, setLoading] = useState(false)
  const [query, setQuery] = useState(searchParams.get('q') || '')
  const [activeTab, setActiveTab] = useState('all')

  useEffect(() => {
    const searchQuery = searchParams.get('q')
    if (searchQuery) {
      setQuery(searchQuery)
      performSearch(searchQuery)
    }
  }, [searchParams])

  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) return

    setLoading(true)
    try {
      const response = await fetch(`/api/search?q=${encodeURIComponent(searchQuery)}`)
      const data = await response.json()

      if (data.success) {
        setResults(data.data)
      }
    } catch (error) {
      console.error('Search error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (newQuery: string) => {
    setQuery(newQuery)
    if (newQuery.trim()) {
      // Update URL
      const url = new URL(window.location.href)
      url.searchParams.set('q', newQuery)
      window.history.pushState({}, '', url.toString())

      performSearch(newQuery)
    }
  }

  const getTotalResults = () => {
    return (results.services?.length || 0) +
           (results.projects?.length || 0) +
           (results.articles?.length || 0) +
           (results.videos?.length || 0) +
           (results.experiences?.length || 0)
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="pt-[120px] pb-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Tìm Kiếm</h1>
            <p className="text-xl text-gray-600">Tìm kiếm nội dung bạn quan tâm</p>
          </div>

          <div className="max-w-2xl mx-auto">
            <SearchBox
              placeholder="Nhập từ khóa tìm kiếm..."
              value={query}
              onSearch={handleSearch}
              className="w-full"
            />
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin mr-2" />
              <span>Đang tìm kiếm...</span>
            </div>
          ) : query && getTotalResults() > 0 ? (
            <>
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Kết quả tìm kiếm cho "{query}"
                </h2>
                <p className="text-gray-600">
                  Tìm thấy {getTotalResults()} kết quả
                </p>
              </div>

              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-6">
                  <TabsTrigger value="all">
                    Tất cả ({getTotalResults()})
                  </TabsTrigger>
                  <TabsTrigger value="services">
                    Dịch vụ ({results.services?.length || 0})
                  </TabsTrigger>
                  <TabsTrigger value="projects">
                    Dự án ({results.projects?.length || 0})
                  </TabsTrigger>
                  <TabsTrigger value="articles">
                    Bài viết ({results.articles?.length || 0})
                  </TabsTrigger>
                  <TabsTrigger value="videos">
                    Video ({results.videos?.length || 0})
                  </TabsTrigger>
                  <TabsTrigger value="experiences">
                    Kinh nghiệm ({results.experiences?.length || 0})
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="all" className="mt-8">
                  <div className="space-y-12">
                    {/* Services */}
                    {results.services && results.services.length > 0 && (
                      <div>
                        <h3 className="text-xl font-bold mb-4">Dịch vụ</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                          {results.services.map((service) => (
                            <div key={service.id} className="bg-white rounded-lg shadow-lg p-6">
                              <h4 className="font-bold text-lg mb-2">{service.title}</h4>
                              <p className="text-gray-600 mb-4">{service.description}</p>
                              <Badge>{service.category_name}</Badge>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Projects */}
                    {results.projects && results.projects.length > 0 && (
                      <div>
                        <h3 className="text-xl font-bold mb-4">Dự án</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                          {results.projects.map((project) => (
                            <ProjectCard key={project.id} project={project} />
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Articles */}
                    {results.articles && results.articles.length > 0 && (
                      <div>
                        <h3 className="text-xl font-bold mb-4">Bài viết</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                          {results.articles.map((article) => (
                            <ArticleCard key={article.id} article={article} />
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Videos */}
                    {results.videos && results.videos.length > 0 && (
                      <div>
                        <h3 className="text-xl font-bold mb-4">Video</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                          {results.videos.map((video) => (
                            <VideoCard key={video.id} video={video} />
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="services" className="mt-8">
                  {results.services && results.services.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {results.services.map((service) => (
                        <div key={service.id} className="bg-white rounded-lg shadow-lg p-6">
                          <h4 className="font-bold text-lg mb-2">{service.title}</h4>
                          <p className="text-gray-600 mb-4">{service.description}</p>
                          <Badge>{service.category_name}</Badge>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-center text-gray-500 py-8">Không tìm thấy dịch vụ nào.</p>
                  )}
                </TabsContent>

                <TabsContent value="projects" className="mt-8">
                  {results.projects && results.projects.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {results.projects.map((project) => (
                        <ProjectCard key={project.id} project={project} />
                      ))}
                    </div>
                  ) : (
                    <p className="text-center text-gray-500 py-8">Không tìm thấy dự án nào.</p>
                  )}
                </TabsContent>

                <TabsContent value="articles" className="mt-8">
                  {results.articles && results.articles.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {results.articles.map((article) => (
                        <ArticleCard key={article.id} article={article} />
                      ))}
                    </div>
                  ) : (
                    <p className="text-center text-gray-500 py-8">Không tìm thấy bài viết nào.</p>
                  )}
                </TabsContent>

                <TabsContent value="videos" className="mt-8">
                  {results.videos && results.videos.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {results.videos.map((video) => (
                        <VideoCard key={video.id} video={video} />
                      ))}
                    </div>
                  ) : (
                    <p className="text-center text-gray-500 py-8">Không tìm thấy video nào.</p>
                  )}
                </TabsContent>

                <TabsContent value="experiences" className="mt-8">
                  {results.experiences && results.experiences.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {results.experiences.map((experience) => (
                        <div key={experience.id} className="bg-white rounded-lg shadow-lg p-6">
                          <h4 className="font-bold text-lg mb-2">{experience.title}</h4>
                          <p className="text-gray-600 mb-4">{experience.excerpt}</p>
                          <div className="flex items-center justify-between text-sm text-gray-500">
                            <span>{experience.author}</span>
                            <Badge>{experience.category_name}</Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-center text-gray-500 py-8">Không tìm thấy kinh nghiệm nào.</p>
                  )}
                </TabsContent>
              </Tabs>
            </>
          ) : query && !loading ? (
            <div className="text-center py-12">
              <Search className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                Không tìm thấy kết quả cho "{query}"
              </h3>
              <p className="text-gray-600 mb-6">
                Hãy thử tìm kiếm với từ khóa khác hoặc kiểm tra lại chính tả.
              </p>
              <Button onClick={() => setQuery('')}>
                Xóa tìm kiếm
              </Button>
            </div>
          ) : (
            <div className="text-center py-12">
              <Search className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                Nhập từ khóa để bắt đầu tìm kiếm
              </h3>
              <p className="text-gray-600">
                Tìm kiếm dịch vụ, dự án, bài viết, video và kinh nghiệm.
              </p>
            </div>
          )}
        </div>
      </section>

      <Footer />
      <FloatingActionBar />
    </div>
  )
}

export default function SearchPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    }>
      <SearchPageContent />
    </Suspense>
  )
}
