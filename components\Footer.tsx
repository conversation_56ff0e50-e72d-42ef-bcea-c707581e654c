import { Phone, Mail, MapPin, Facebook, Youtube } from "lucide-react"

export default function Footer() {
  return (
    <footer className="bg-[#212121] text-white">
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <h3 className="text-2xl font-bold mb-6">KIẾN TRÚC SƯ THÁI VŨ</h3>
            <p className="text-gray-300 leading-relaxed mb-6">
              Chuyên thiết kế và thi công nội thất cao cấp, mang đến không gian sống hoàn hảo cho gia đình Việt.
            </p>
            <div className="flex space-x-4">
              <Facebook className="w-6 h-6 cursor-pointer hover:text-[#3B82F6] transition-colors" />
              <Youtube className="w-6 h-6 cursor-pointer hover:text-[#FF0000] transition-colors" />
            </div>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-xl font-bold mb-6">DỊCH VỤ</h4>
            <ul className="space-y-3 text-gray-300">
              <li>
                <a href="#" className="hover:text-white transition-colors">
                  Thiết kế nội thất
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-white transition-colors">
                  Thi công nội thất
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-white transition-colors">
                  Bếp Nhật Bản
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-white transition-colors">
                  Nội thất cao cấp
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-white transition-colors">
                  Tư vấn thiết kế
                </a>
              </li>
            </ul>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-xl font-bold mb-6">LIÊN KẾT NHANH</h4>
            <ul className="space-y-3 text-gray-300">
              <li>
                <a href="#" className="hover:text-white transition-colors">
                  Giới thiệu
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-white transition-colors">
                  Dự án
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-white transition-colors">
                  Cẩm nang
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-white transition-colors">
                  Tin tức
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-white transition-colors">
                  Liên hệ
                </a>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-xl font-bold mb-6">THÔNG TIN LIÊN HỆ</h4>
            <div className="space-y-4 text-gray-300">
              <div className="flex items-start gap-3">
                <MapPin className="w-5 h-5 mt-1 flex-shrink-0" />
                <span>123 Đường ABC, Quận 1, TP.HCM</span>
              </div>
              <div className="flex items-center gap-3">
                <Phone className="w-5 h-5 flex-shrink-0" />
                <span>0123 456 789</span>
              </div>
              <div className="flex items-center gap-3">
                <Mail className="w-5 h-5 flex-shrink-0" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-12 pt-8 text-center text-gray-400">
          <p>&copy; 2024 Kiến Trúc Sư Thái Vũ. Tất cả quyền được bảo lưu.</p>
        </div>
      </div>
    </footer>
  )
}
