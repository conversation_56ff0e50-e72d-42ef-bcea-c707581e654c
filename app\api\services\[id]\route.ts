import { NextRequest, NextResponse } from 'next/server'
import { ServiceModel } from '@/lib/models/service'

// GET /api/services/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid service ID' },
        { status: 400 }
      )
    }

    const service = await ServiceModel.getById(id)
    if (!service) {
      return NextResponse.json(
        { success: false, error: 'Service not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: service
    })
  } catch (error) {
    console.error('Service API Error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch service' },
      { status: 500 }
    )
  }
}

// PUT /api/services/[id]
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid service ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const {
      title,
      slug,
      description,
      content,
      image,
      features,
      price_range,
      duration,
      category_id,
      featured,
      status
    } = body

    const updated = await ServiceModel.update(id, {
      title,
      slug,
      description,
      content,
      image,
      features,
      price_range,
      duration,
      category_id,
      featured,
      status
    })

    if (!updated) {
      return NextResponse.json(
        { success: false, error: 'Service not found' },
        { status: 404 }
      )
    }

    const service = await ServiceModel.getById(id)
    return NextResponse.json({
      success: true,
      data: service
    })
  } catch (error) {
    console.error('Service API Error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update service' },
      { status: 500 }
    )
  }
}

// DELETE /api/services/[id]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid service ID' },
        { status: 400 }
      )
    }

    const deleted = await ServiceModel.delete(id)
    if (!deleted) {
      return NextResponse.json(
        { success: false, error: 'Service not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Service deleted successfully'
    })
  } catch (error) {
    console.error('Service API Error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete service' },
      { status: 500 }
    )
  }
}
