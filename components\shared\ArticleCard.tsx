import Image from 'next/image'
import Link from 'next/link'
import { Badge } from '@/components/ui/badge'
import { Calendar, User, Eye } from 'lucide-react'

interface Article {
  id: number
  title: string
  slug: string
  excerpt?: string
  image?: string
  author?: string
  category_name?: string
  views?: number
  published_date?: string
  featured?: boolean
}

interface ArticleCardProps {
  article: Article
  showCategory?: boolean
  showAuthor?: boolean
  showViews?: boolean
  className?: string
}

export default function ArticleCard({ 
  article, 
  showCategory = true,
  showAuthor = true,
  showViews = true,
  className = ""
}: ArticleCardProps) {
  const defaultImage = "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600"

  return (
    <div className={`bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 ${className}`}>
      <div className="relative h-48">
        <Image
          src={article.image || defaultImage}
          alt={article.title}
          fill
          className="object-cover"
        />
        {article.featured && (
          <div className="absolute top-4 left-4">
            <Badge className="bg-red-500 text-white">
              Nổi bật
            </Badge>
          </div>
        )}
        {showCategory && article.category_name && (
          <div className="absolute top-4 right-4">
            <Badge className="bg-blue-500 text-white">
              {article.category_name}
            </Badge>
          </div>
        )}
      </div>
      
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
          <Link 
            href={`/cam-nang/${article.slug}`}
            className="hover:text-blue-600 transition-colors"
          >
            {article.title}
          </Link>
        </h3>
        
        {article.excerpt && (
          <p className="text-gray-600 mb-4 line-clamp-3">
            {article.excerpt}
          </p>
        )}
        
        <div className="flex items-center justify-between text-sm text-gray-500">
          <div className="flex items-center space-x-4">
            {showAuthor && article.author && (
              <div className="flex items-center">
                <User className="h-4 w-4 mr-1" />
                {article.author}
              </div>
            )}
            
            {article.published_date && (
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                {new Date(article.published_date).toLocaleDateString('vi-VN')}
              </div>
            )}
          </div>
          
          {showViews && article.views !== undefined && (
            <div className="flex items-center">
              <Eye className="h-4 w-4 mr-1" />
              {article.views.toLocaleString()}
            </div>
          )}
        </div>
        
        <div className="mt-4">
          <Link 
            href={`/cam-nang/${article.slug}`}
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            Đọc thêm →
          </Link>
        </div>
      </div>
    </div>
  )
}
