import { NextRequest, NextResponse } from 'next/server'
import { ProjectModel } from '@/lib/models/project'

// GET /api/projects
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category_id = searchParams.get('category_id')
    const featured = searchParams.get('featured')
    const status = searchParams.get('status')
    const limit = searchParams.get('limit')
    const offset = searchParams.get('offset')
    const search = searchParams.get('search')
    const year = searchParams.get('year')

    // Handle search
    if (search) {
      const projects = await ProjectModel.search(search, limit ? parseInt(limit) : 10)
      return NextResponse.json({
        success: true,
        data: projects
      })
    }

    // Handle year filter
    if (year) {
      const projects = await ProjectModel.getByYear(parseInt(year))
      return NextResponse.json({
        success: true,
        data: projects
      })
    }

    // Handle regular filtering
    const filters: any = {}

    if (category_id) filters.category_id = parseInt(category_id)
    if (featured !== null) filters.featured = featured === 'true'
    if (status) filters.status = status
    if (limit) filters.limit = parseInt(limit)
    if (offset) filters.offset = parseInt(offset)

    const projects = await ProjectModel.getAll(filters)

    return NextResponse.json({
      success: true,
      data: projects
    })
  } catch (error) {
    console.error('Projects API Error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch projects'
      },
      { status: 500 }
    )
  }
}

// POST /api/projects
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      title,
      slug,
      description,
      content,
      area,
      location,
      year,
      images,
      category_id,
      featured,
      status
    } = body

    if (!title) {
      return NextResponse.json(
        {
          success: false,
          error: 'Title is required'
        },
        { status: 400 }
      )
    }

    const projectId = await ProjectModel.create({
      title,
      slug,
      description,
      content,
      area,
      location,
      year,
      images,
      category_id,
      featured,
      status
    })

    const project = await ProjectModel.getById(projectId)

    return NextResponse.json({
      success: true,
      data: project
    }, { status: 201 })
  } catch (error) {
    console.error('Projects API Error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create project'
      },
      { status: 500 }
    )
  }
}
