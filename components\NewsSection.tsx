"use client"
import Image from "next/image"
import { Calendar, User, Loader2, Eye } from "lucide-react"
import { useFeaturedArticles } from "@/hooks/use-api"

export default function NewsSection() {
  const { data: articles, loading, error } = useFeaturedArticles(3)

  if (loading) {
    return (
      <section className="py-20 bg-[#F7F7F7]">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-[#1F2937] mb-6 tracking-wider">KINH NGHIỆM & TIN TỨC</h2>
            <p className="text-xl text-[#6B7280] max-w-3xl mx-auto">
              Cập nhật những kiến thức và xu hướng mới nhất trong lĩnh vực nội thất
            </p>
          </div>
          <div className="flex justify-center">
            <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
          </div>
        </div>
      </section>
    )
  }

  if (error) {
    return (
      <section className="py-20 bg-[#F7F7F7]">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p className="text-red-600">Không thể tải bài viết: {error}</p>
          </div>
        </div>
      </section>
    )
  }
  return (
    <section className="py-20 bg-[#F7F7F7]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-[#1F2937] mb-6 tracking-wider">KINH NGHIỆM & TIN TỨC</h2>
          <p className="text-xl text-[#6B7280] max-w-3xl mx-auto">
            Cập nhật những kiến thức và xu hướng mới nhất trong lĩnh vực nội thất
          </p>
        </div>

        {articles && articles.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {articles.map((article: any) => (
              <article
                key={article.id}
                className="bg-white rounded-lg shadow-lg overflow-hidden group hover:shadow-xl transition-all duration-300 hover:transform hover:scale-105"
              >
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={article.image || "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=250"}
                    alt={article.title}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-110"
                  />
                  {article.category_name && (
                    <div className="absolute top-4 left-4">
                      <span className="bg-blue-600/90 text-white text-xs px-2 py-1 rounded-full">
                        {article.category_name}
                      </span>
                    </div>
                  )}
                </div>
                <div className="p-6">
                  <div className="flex items-center gap-4 text-sm text-[#6B7280] mb-3">
                    <span className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {article.published_date ? new Date(article.published_date).toLocaleDateString('vi-VN') : 'N/A'}
                    </span>
                    {article.author && (
                      <span className="flex items-center gap-1">
                        <User className="w-4 h-4" />
                        {article.author}
                      </span>
                    )}
                    {article.views && (
                      <span className="flex items-center gap-1">
                        <Eye className="w-4 h-4" />
                        {article.views}
                      </span>
                    )}
                  </div>
                  <h3 className="text-xl font-bold text-[#1F2937] mb-3 group-hover:text-[#3B82F6] transition-colors">
                    {article.title}
                  </h3>
                  <p className="text-[#6B7280] leading-relaxed mb-4">
                    {article.excerpt || 'Nội dung bài viết...'}
                  </p>
                  <button className="text-[#3B82F6] font-semibold hover:text-[#2563EB] transition-colors">
                    Đọc thêm →
                  </button>
                </div>
              </article>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500">Chưa có bài viết nào được hiển thị</p>
          </div>
        )}

        <div className="text-center mt-12">
          <button className="bg-[#3B82F6] hover:bg-[#2563EB] text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors">
            XEM TẤT CẢ BÀI VIẾT
          </button>
        </div>
      </div>
    </section>
  )
}
