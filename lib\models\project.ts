import { executeQuery, generateSlug } from '../database'

export interface Project {
  id?: number
  title: string
  slug: string
  description?: string
  content?: string
  area?: string
  location?: string
  completed_date?: string
  image?: string
  images?: string[] // Will be stored as JSON
  category_id?: number
  featured?: boolean
  status?: 'draft' | 'published' | 'archived'
  created_at?: string
  updated_at?: string
  // Joined fields
  category_name?: string
}

export class ProjectModel {
  // Get all projects with optional filters
  static async getAll(filters: {
    category_id?: number
    featured?: boolean
    status?: string
    limit?: number
    offset?: number
  } = {}): Promise<Project[]> {
    let query = `
      SELECT p.*, c.name as category_name
      FROM projects p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE 1=1
    `
    const params: any[] = []

    if (filters.category_id) {
      query += ' AND p.category_id = ?'
      params.push(filters.category_id)
    }

    if (filters.featured !== undefined) {
      query += ' AND p.featured = ?'
      params.push(filters.featured)
    }

    if (filters.status) {
      query += ' AND p.status = ?'
      params.push(filters.status)
    } else {
      query += ' AND p.status = "published"'
    }

    query += ' ORDER BY p.featured DESC, p.year DESC, p.created_at DESC'

    if (filters.limit) {
      query += ' LIMIT ?'
      params.push(filters.limit)

      if (filters.offset) {
        query += ' OFFSET ?'
        params.push(filters.offset)
      }
    }

    const results = await executeQuery<Project>(query, params)

    // Parse JSON images
    return results.map(project => ({
      ...project,
      images: project.images ? JSON.parse(project.images as any) : []
    }))
  }

  // Get project by ID
  static async getById(id: number): Promise<Project | null> {
    const query = `
      SELECT p.*, c.name as category_name
      FROM projects p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.id = ?
    `
    const results = await executeQuery<Project>(query, [id])

    if (results.length === 0) return null

    const project = results[0]
    return {
      ...project,
      images: project.images ? JSON.parse(project.images as any) : []
    }
  }

  // Get project by slug
  static async getBySlug(slug: string): Promise<Project | null> {
    const query = `
      SELECT p.*, c.name as category_name
      FROM projects p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.slug = ? AND p.status = 'published'
    `
    const results = await executeQuery<Project>(query, [slug])

    if (results.length === 0) return null

    const project = results[0]
    return {
      ...project,
      images: project.images ? JSON.parse(project.images as any) : []
    }
  }

  // Create new project
  static async create(data: Omit<Project, 'id' | 'created_at' | 'updated_at'>): Promise<number> {
    const slug = data.slug || generateSlug(data.title)

    const query = `
      INSERT INTO projects (
        title, slug, description, content, area, location, 
        year, images, category_id, featured, status
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `

    const result = await executeQuery(query, [
      data.title,
      slug,
      data.description || null,
      data.content || null,
      data.area || null,
      data.location || null,
      data.year || new Date().getFullYear(),
      data.images ? JSON.stringify(data.images) : null,
      data.category_id || null,
      data.featured || false,
      data.status || 'published'
    ])

    return (result as any).insertId
  }

  // Update project
  static async update(id: number, data: Partial<Omit<Project, 'id' | 'created_at' | 'updated_at'>>): Promise<boolean> {
    const fields: string[] = []
    const values: any[] = []

    if (data.title !== undefined) {
      fields.push('title = ?')
      values.push(data.title)
    }

    if (data.slug !== undefined) {
      fields.push('slug = ?')
      values.push(data.slug)
    }

    if (data.description !== undefined) {
      fields.push('description = ?')
      values.push(data.description)
    }

    if (data.content !== undefined) {
      fields.push('content = ?')
      values.push(data.content)
    }

    if (data.area !== undefined) {
      fields.push('area = ?')
      values.push(data.area)
    }

    if (data.location !== undefined) {
      fields.push('location = ?')
      values.push(data.location)
    }

    if (data.year !== undefined) {
      fields.push('year = ?')
      values.push(data.year)
    }

    if (data.images !== undefined) {
      fields.push('images = ?')
      values.push(JSON.stringify(data.images))
    }

    if (data.category_id !== undefined) {
      fields.push('category_id = ?')
      values.push(data.category_id)
    }

    if (data.featured !== undefined) {
      fields.push('featured = ?')
      values.push(data.featured)
    }

    if (data.status !== undefined) {
      fields.push('status = ?')
      values.push(data.status)
    }

    if (fields.length === 0) {
      return false
    }

    values.push(id)

    const query = `
      UPDATE projects 
      SET ${fields.join(', ')}
      WHERE id = ?
    `

    const result = await executeQuery(query, values)
    return (result as any).affectedRows > 0
  }

  // Delete project
  static async delete(id: number): Promise<boolean> {
    const query = `
      DELETE FROM projects 
      WHERE id = ?
    `
    const result = await executeQuery(query, [id])
    return (result as any).affectedRows > 0
  }

  // Get featured projects
  static async getFeatured(limit: number = 6): Promise<Project[]> {
    return this.getAll({ featured: true, limit })
  }

  // Search projects
  static async search(searchTerm: string, limit: number = 10): Promise<Project[]> {
    const query = `
      SELECT p.*, c.name as category_name
      FROM projects p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.status = 'published' 
        AND (p.title LIKE ? OR p.description LIKE ? OR p.content LIKE ? OR p.location LIKE ?)
      ORDER BY p.featured DESC, p.year DESC, p.created_at DESC
      LIMIT ?
    `

    const searchPattern = `%${searchTerm}%`
    const results = await executeQuery<Project>(query, [searchPattern, searchPattern, searchPattern, searchPattern, limit])

    return results.map(project => ({
      ...project,
      images: project.images ? JSON.parse(project.images as any) : []
    }))
  }

  // Get projects by year
  static async getByYear(year: number): Promise<Project[]> {
    const query = `
      SELECT p.*, c.name as category_name
      FROM projects p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.status = 'published' 
        AND p.year = ?
      ORDER BY p.year DESC
    `

    const results = await executeQuery<Project>(query, [year])

    return results.map(project => ({
      ...project,
      images: project.images ? JSON.parse(project.images as any) : []
    }))
  }

  // Change project status
  static async changeStatus(id: number, status: 'draft' | 'published' | 'archived'): Promise<boolean> {
    const query = `
      UPDATE projects 
      SET status = ?
      WHERE id = ?
    `
    const result = await executeQuery(query, [status, id])
    return (result as any).affectedRows > 0
  }

  // Publish project
  static async publish(id: number): Promise<boolean> {
    return this.changeStatus(id, 'published')
  }

  // Archive project
  static async archive(id: number): Promise<boolean> {
    return this.changeStatus(id, 'archived')
  }

  // Draft project
  static async draft(id: number): Promise<boolean> {
    return this.changeStatus(id, 'draft')
  }
}
