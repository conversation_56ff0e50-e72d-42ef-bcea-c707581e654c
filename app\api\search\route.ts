import { NextRequest, NextResponse } from 'next/server'
import { ServiceModel } from '@/lib/models/service'
import { ProjectModel } from '@/lib/models/project'
import { ArticleModel } from '@/lib/models/article'
import { VideoModel } from '@/lib/models/video'
import { ExperienceModel } from '@/lib/models/experience'

// GET /api/search
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const type = searchParams.get('type') as 'services' | 'projects' | 'articles' | 'videos' | 'experiences' | null
    const limit = parseInt(searchParams.get('limit') || '10')

    if (!query) {
      return NextResponse.json(
        { success: false, error: 'Search query is required' },
        { status: 400 }
      )
    }

    let results: any = {}

    if (type) {
      // Search specific type
      switch (type) {
        case 'services':
          results.services = await ServiceModel.search(query, limit)
          break
        case 'projects':
          results.projects = await ProjectModel.search(query, limit)
          break
        case 'articles':
          results.articles = await ArticleModel.search(query, limit)
          break
        case 'videos':
          results.videos = await VideoModel.search(query, limit)
          break
        case 'experiences':
          results.experiences = await ExperienceModel.search(query, limit)
          break
      }
    } else {
      // Search all types
      const searchLimit = Math.ceil(limit / 5) // Distribute limit across types
      
      const [services, projects, articles, videos, experiences] = await Promise.all([
        ServiceModel.search(query, searchLimit),
        ProjectModel.search(query, searchLimit),
        ArticleModel.search(query, searchLimit),
        VideoModel.search(query, searchLimit),
        ExperienceModel.search(query, searchLimit)
      ])

      results = {
        services,
        projects,
        articles,
        videos,
        experiences,
        total: services.length + projects.length + articles.length + videos.length + experiences.length
      }
    }

    return NextResponse.json({
      success: true,
      data: results,
      query
    })
  } catch (error) {
    console.error('Search API Error:', error)
    return NextResponse.json(
      { success: false, error: 'Search failed' },
      { status: 500 }
    )
  }
}
