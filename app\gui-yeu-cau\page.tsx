"use client"

import { useState } from "react"
import { Metadata } from "next"
import Header from "@/components/Header"
import Footer from "@/components/Footer"
import FloatingActionBar from "@/components/FloatingActionBar"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Phone, Mail, MapPin, Clock, Send, Upload } from "lucide-react"

// Note: Metadata export needs to be in a separate file for client components
// export const metadata: Metadata = {
//   title: "Gửi Yêu Cầu Tư Vấn - Kiến Tr<PERSON>c <PERSON>",
//   description: "<PERSON><PERSON><PERSON> yêu cầu tư vấn thiết kế nội thất miễn phí",
// }

const serviceTypes = [
  { value: "thiet-ke", label: "Thiết Kế Nội Thất" },
  { value: "thi-cong", label: "Thi Công Trọn Gói" },
  { value: "tu-van", label: "Tư Vấn Thiết Kế" },
  { value: "bep-nhat", label: "Bếp Nhật Bản" },
  { value: "khac", label: "Dịch Vụ Khác" }
]

const budgetRanges = [
  { value: "duoi-200", label: "Dưới 200 triệu" },
  { value: "200-500", label: "200 - 500 triệu" },
  { value: "500-1000", label: "500 triệu - 1 tỷ" },
  { value: "1000-2000", label: "1 - 2 tỷ" },
  { value: "tren-2000", label: "Trên 2 tỷ" },
  { value: "chua-xac-dinh", label: "Chưa xác định" }
]

const projectTypes = [
  { value: "nha-pho", label: "Nhà Phố" },
  { value: "chung-cu", label: "Chung Cư" },
  { value: "biet-thu", label: "Biệt Thự" },
  { value: "van-phong", label: "Văn Phòng" },
  { value: "showroom", label: "Showroom" },
  { value: "khac", label: "Khác" }
]

export default function GuiYeuCauPage() {
  const [formData, setFormData] = useState({
    fullName: "",
    phone: "",
    email: "",
    address: "",
    serviceType: "",
    projectType: "",
    area: "",
    budget: "",
    timeline: "",
    description: "",
    hasDrawing: false,
    agreeTerms: false
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    alert("Cảm ơn bạn đã gửi yêu cầu! Chúng tôi sẽ liên hệ trong vòng 24h.")
    setIsSubmitting(false)
    
    // Reset form
    setFormData({
      fullName: "",
      phone: "",
      email: "",
      address: "",
      serviceType: "",
      projectType: "",
      area: "",
      budget: "",
      timeline: "",
      description: "",
      hasDrawing: false,
      agreeTerms: false
    })
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Banner */}
      <section className="relative h-[400px] mt-[120px]">
        <div
          className="w-full h-full bg-cover bg-center"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=800')`
          }}
        >
          <div className="absolute inset-0 bg-black bg-opacity-50" />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-white">
              <h1 className="text-5xl md:text-6xl font-bold mb-4">GỬI YÊU CẦU TƯ VẤN</h1>
              <p className="text-xl md:text-2xl">Nhận tư vấn miễn phí từ chuyên gia</p>
            </div>
          </div>
        </div>
      </section>

      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Contact Form */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-2xl text-[#1F2937]">Thông Tin Dự Án</CardTitle>
                  <CardDescription>
                    Vui lòng điền đầy đủ thông tin để chúng tôi tư vấn chính xác nhất
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Personal Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-[#1F2937] mb-2">
                          Họ và Tên *
                        </label>
                        <Input
                          required
                          value={formData.fullName}
                          onChange={(e) => setFormData({...formData, fullName: e.target.value})}
                          placeholder="Nhập họ và tên"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-[#1F2937] mb-2">
                          Số Điện Thoại *
                        </label>
                        <Input
                          required
                          type="tel"
                          value={formData.phone}
                          onChange={(e) => setFormData({...formData, phone: e.target.value})}
                          placeholder="Nhập số điện thoại"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-[#1F2937] mb-2">
                          Email
                        </label>
                        <Input
                          type="email"
                          value={formData.email}
                          onChange={(e) => setFormData({...formData, email: e.target.value})}
                          placeholder="Nhập email"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-[#1F2937] mb-2">
                          Địa Chỉ Dự Án
                        </label>
                        <Input
                          value={formData.address}
                          onChange={(e) => setFormData({...formData, address: e.target.value})}
                          placeholder="Nhập địa chỉ dự án"
                        />
                      </div>
                    </div>

                    {/* Project Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-[#1F2937] mb-2">
                          Loại Dịch Vụ *
                        </label>
                        <Select value={formData.serviceType} onValueChange={(value) => setFormData({...formData, serviceType: value})}>
                          <SelectTrigger>
                            <SelectValue placeholder="Chọn loại dịch vụ" />
                          </SelectTrigger>
                          <SelectContent>
                            {serviceTypes.map((service) => (
                              <SelectItem key={service.value} value={service.value}>
                                {service.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-[#1F2937] mb-2">
                          Loại Công Trình
                        </label>
                        <Select value={formData.projectType} onValueChange={(value) => setFormData({...formData, projectType: value})}>
                          <SelectTrigger>
                            <SelectValue placeholder="Chọn loại công trình" />
                          </SelectTrigger>
                          <SelectContent>
                            {projectTypes.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                {type.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-[#1F2937] mb-2">
                          Diện Tích (m²)
                        </label>
                        <Input
                          type="number"
                          value={formData.area}
                          onChange={(e) => setFormData({...formData, area: e.target.value})}
                          placeholder="Nhập diện tích"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-[#1F2937] mb-2">
                          Ngân Sách Dự Kiến
                        </label>
                        <Select value={formData.budget} onValueChange={(value) => setFormData({...formData, budget: value})}>
                          <SelectTrigger>
                            <SelectValue placeholder="Chọn mức ngân sách" />
                          </SelectTrigger>
                          <SelectContent>
                            {budgetRanges.map((budget) => (
                              <SelectItem key={budget.value} value={budget.value}>
                                {budget.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-[#1F2937] mb-2">
                        Thời Gian Mong Muốn
                      </label>
                      <Input
                        value={formData.timeline}
                        onChange={(e) => setFormData({...formData, timeline: e.target.value})}
                        placeholder="VD: 3 tháng, cuối năm 2024..."
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-[#1F2937] mb-2">
                        Mô Tả Chi Tiết Yêu Cầu
                      </label>
                      <Textarea
                        rows={4}
                        value={formData.description}
                        onChange={(e) => setFormData({...formData, description: e.target.value})}
                        placeholder="Mô tả chi tiết về yêu cầu thiết kế, phong cách mong muốn, số phòng..."
                      />
                    </div>

                    {/* File Upload */}
                    <div>
                      <label className="block text-sm font-medium text-[#1F2937] mb-2">
                        Tài Liệu Đính Kèm
                      </label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-600 mb-2">
                          Kéo thả file hoặc click để chọn
                        </p>
                        <p className="text-xs text-gray-500">
                          Hỗ trợ: JPG, PNG, PDF (tối đa 10MB)
                        </p>
                        <Button type="button" variant="outline" className="mt-2">
                          Chọn File
                        </Button>
                      </div>
                    </div>

                    {/* Checkboxes */}
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="hasDrawing"
                          checked={formData.hasDrawing}
                          onCheckedChange={(checked) => setFormData({...formData, hasDrawing: checked as boolean})}
                        />
                        <label htmlFor="hasDrawing" className="text-sm text-[#1F2937]">
                          Tôi đã có bản vẽ thiết kế sẵn
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="agreeTerms"
                          checked={formData.agreeTerms}
                          onCheckedChange={(checked) => setFormData({...formData, agreeTerms: checked as boolean})}
                        />
                        <label htmlFor="agreeTerms" className="text-sm text-[#1F2937]">
                          Tôi đồng ý với <a href="#" className="text-[#3B82F6] hover:underline">điều khoản sử dụng</a> và <a href="#" className="text-[#3B82F6] hover:underline">chính sách bảo mật</a>
                        </label>
                      </div>
                    </div>

                    <Button
                      type="submit"
                      disabled={isSubmitting || !formData.agreeTerms}
                      className="w-full bg-[#3B82F6] hover:bg-[#2563EB] text-white py-3"
                    >
                      {isSubmitting ? (
                        "Đang Gửi..."
                      ) : (
                        <>
                          <Send className="w-4 h-4 mr-2" />
                          Gửi Yêu Cầu Tư Vấn
                        </>
                      )}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* Contact Information */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-xl text-[#1F2937]">Thông Tin Liên Hệ</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-[#3B82F6] rounded-full flex items-center justify-center">
                      <Phone className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-[#1F2937]">Hotline</p>
                      <p className="text-[#6B7280]">0935 754 588</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-[#3B82F6] rounded-full flex items-center justify-center">
                      <Mail className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-[#1F2937]">Email</p>
                      <p className="text-[#6B7280]"><EMAIL></p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-[#3B82F6] rounded-full flex items-center justify-center">
                      <MapPin className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-[#1F2937]">Địa Chỉ</p>
                      <p className="text-[#6B7280]">Buôn Ma Thuột, Đắk Lắk</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-[#3B82F6] rounded-full flex items-center justify-center">
                      <Clock className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-[#1F2937]">Giờ Làm Việc</p>
                      <p className="text-[#6B7280]">8:00 - 18:00 (T2-T7)</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-xl text-[#1F2937]">Quy Trình Tư Vấn</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex gap-3">
                      <div className="w-6 h-6 bg-[#3B82F6] rounded-full flex items-center justify-center text-white text-sm font-bold">1</div>
                      <div>
                        <p className="font-medium text-[#1F2937]">Tiếp Nhận Yêu Cầu</p>
                        <p className="text-sm text-[#6B7280]">Trong vòng 2 giờ</p>
                      </div>
                    </div>
                    <div className="flex gap-3">
                      <div className="w-6 h-6 bg-[#3B82F6] rounded-full flex items-center justify-center text-white text-sm font-bold">2</div>
                      <div>
                        <p className="font-medium text-[#1F2937]">Tư Vấn Sơ Bộ</p>
                        <p className="text-sm text-[#6B7280]">Qua điện thoại</p>
                      </div>
                    </div>
                    <div className="flex gap-3">
                      <div className="w-6 h-6 bg-[#3B82F6] rounded-full flex items-center justify-center text-white text-sm font-bold">3</div>
                      <div>
                        <p className="font-medium text-[#1F2937]">Khảo Sát Thực Tế</p>
                        <p className="text-sm text-[#6B7280]">Miễn phí tại nhà</p>
                      </div>
                    </div>
                    <div className="flex gap-3">
                      <div className="w-6 h-6 bg-[#3B82F6] rounded-full flex items-center justify-center text-white text-sm font-bold">4</div>
                      <div>
                        <p className="font-medium text-[#1F2937]">Báo Giá Chi Tiết</p>
                        <p className="text-sm text-[#6B7280]">Trong 24-48h</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <Footer />
      <FloatingActionBar />
    </div>
  )
}
