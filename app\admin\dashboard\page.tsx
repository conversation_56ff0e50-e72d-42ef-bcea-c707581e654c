"use client"

import { useEffect, useState } from "react"
// import { motion } from "framer-motion"
import {
  LayoutDashboard,
  Briefcase,
  FolderOpen,
  FileText,
  Video,
  Users,
  Eye,
  TrendingUp,
  Calendar,
  ArrowUpRight,
  Activity
} from "lucide-react"
import { apiClient } from "@/lib/api-client"
// import {
//   fadeInUp,
//   staggerContainer,
//   staggerItem,
//   cardHover,
//   loadingPulse,
//   getMotionProps
// } from "@/lib/motion"

interface DashboardStats {
  services: number
  projects: number
  articles: number
  videos: number
  experiences: number
  totalViews: number
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    services: 0,
    projects: 0,
    articles: 0,
    videos: 0,
    experiences: 0,
    totalViews: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const [services, projects, articles, videos, experiences] = await Promise.all([
          apiClient.getServices(),
          apiClient.getProjects(),
          apiClient.getArticles(),
          apiClient.getVideos(),
          apiClient.getExperiences()
        ])

        const totalViews = [
          ...(articles.data || []),
          ...(videos.data || []),
          ...(experiences.data || [])
        ].reduce((sum: number, item: any) => sum + (item.views || 0), 0)

        setStats({
          services: services.data?.length || 0,
          projects: projects.data?.length || 0,
          articles: articles.data?.length || 0,
          videos: videos.data?.length || 0,
          experiences: experiences.data?.length || 0,
          totalViews
        })
      } catch (error) {
        console.error('Error fetching stats:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  const statCards = [
    {
      name: 'Dịch Vụ',
      value: stats.services,
      icon: Briefcase,
      color: 'bg-interior-warm-brown',
      href: '/admin/services'
    },
    {
      name: 'Dự Án',
      value: stats.projects,
      icon: FolderOpen,
      color: 'bg-interior-sage-green',
      href: '/admin/projects'
    },
    {
      name: 'Bài Viết',
      value: stats.articles,
      icon: FileText,
      color: 'bg-interior-terracotta',
      href: '/admin/articles'
    },
    {
      name: 'Video',
      value: stats.videos,
      icon: Video,
      color: 'bg-interior-warm-gold',
      href: '/admin/videos'
    },
    {
      name: 'Kinh Nghiệm',
      value: stats.experiences,
      icon: Users,
      color: 'bg-interior-muted-copper',
      href: '/admin/experiences'
    },
    {
      name: 'Tổng Lượt Xem',
      value: stats.totalViews,
      icon: Eye,
      color: 'bg-interior-forest-green',
      href: '#'
    }
  ]

  return (
    <div className="px-4 sm:px-6 lg:px-8 py-6">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="text-3xl">🏠</div>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
              <p className="mt-1 text-sm text-muted-foreground">
                Tổng quan về hệ thống quản lý nội dung thiết kế nội thất
              </p>
            </div>
          </div>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 hidden sm:flex items-center space-x-2 text-sm text-muted-foreground">
          <Activity className="h-4 w-4" />
          <span>Cập nhật lúc {new Date().toLocaleTimeString('vi-VN')}</span>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {statCards.map((card, index) => (
          <div
            key={card.name}
            className="group relative overflow-hidden rounded-xl bg-card border border-border shadow-sm cursor-pointer hover:shadow-md transition-all duration-200 hover:scale-[1.02]"
            onClick={() => card.href !== '#' && (window.location.href = card.href)}
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`inline-flex items-center justify-center p-3 rounded-xl ${card.color} shadow-sm`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-muted-foreground truncate">
                    {card.name}
                  </dt>
                  <dd className="text-2xl font-bold text-foreground">
                    {loading ? '...' : card.value.toLocaleString()}
                  </dd>
                </dl>
              </div>
            </div>
            <div className="absolute inset-0 bg-gradient-to-r from-transparent to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
          </div>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="mt-8">
        <div className="bg-card border border-border shadow-sm rounded-xl">
          <div className="px-6 py-6">
            <h3 className="text-xl font-semibold text-foreground mb-6 flex items-center">
              <TrendingUp className="h-5 w-5 mr-2 text-accent" />
              Hoạt Động Gần Đây
            </h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-4 p-4 rounded-lg bg-accent/5 border border-accent/20">
                <div className="flex-shrink-0">
                  <div className="h-10 w-10 rounded-full bg-accent/20 flex items-center justify-center">
                    <TrendingUp className="h-5 w-5 text-accent" />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-foreground">
                    Hệ thống đã được khởi tạo thành công
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Database MariaDB đã sẵn sàng cho thiết kế nội thất
                  </p>
                </div>
                <div className="flex-shrink-0 text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4 inline mr-1" />
                  Hôm nay
                </div>
              </div>

              <div className="flex items-center space-x-4 p-4 rounded-lg bg-primary/5 border border-primary/20">
                <div className="flex-shrink-0">
                  <div className="h-10 w-10 rounded-full bg-primary/20 flex items-center justify-center">
                    <FileText className="h-5 w-5 text-primary" />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-foreground">
                    API endpoints đã được tạo
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Tất cả CRUD operations đã sẵn sàng
                  </p>
                </div>
                <div className="flex-shrink-0 text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4 inline mr-1" />
                  Hôm nay
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Thao Tác Nhanh
            </h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <Briefcase className="h-4 w-4 mr-2" />
                Thêm Dịch Vụ
              </button>
              <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                <FolderOpen className="h-4 w-4 mr-2" />
                Thêm Dự Án
              </button>
              <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700">
                <FileText className="h-4 w-4 mr-2" />
                Viết Bài
              </button>
              <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
                <Video className="h-4 w-4 mr-2" />
                Thêm Video
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
