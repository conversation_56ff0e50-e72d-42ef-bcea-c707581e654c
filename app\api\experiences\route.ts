import { NextRequest, NextResponse } from 'next/server'
import { ExperienceModel } from '@/lib/models/experience'

// GET /api/experiences
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category_id = searchParams.get('category_id')
    const featured = searchParams.get('featured')
    const status = searchParams.get('status')
    const limit = searchParams.get('limit')
    const offset = searchParams.get('offset')
    const search = searchParams.get('search')
    const popular = searchParams.get('popular')

    // Handle search
    if (search) {
      const experiences = await ExperienceModel.search(search, limit ? parseInt(limit) : 10)
      return NextResponse.json({
        success: true,
        data: experiences
      })
    }

    // Handle popular experiences
    if (popular === 'true') {
      const experiences = await ExperienceModel.getPopular(limit ? parseInt(limit) : 10)
      return NextResponse.json({
        success: true,
        data: experiences
      })
    }

    // Handle regular filtering
    const filters: any = {}
    
    if (category_id) filters.category_id = parseInt(category_id)
    if (featured !== null) filters.featured = featured === 'true'
    if (status) filters.status = status
    if (limit) filters.limit = parseInt(limit)
    if (offset) filters.offset = parseInt(offset)

    const experiences = await ExperienceModel.getAll(filters)

    return NextResponse.json({
      success: true,
      data: experiences
    })
  } catch (error) {
    console.error('Experiences API Error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch experiences'
      },
      { status: 500 }
    )
  }
}

// POST /api/experiences
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      title,
      slug,
      excerpt,
      content,
      image,
      author,
      author_avatar,
      category_id,
      featured,
      views,
      likes,
      read_time,
      status,
      published_date
    } = body

    if (!title) {
      return NextResponse.json(
        {
          success: false,
          error: 'Title is required'
        },
        { status: 400 }
      )
    }

    const experienceId = await ExperienceModel.create({
      title,
      slug,
      excerpt,
      content,
      image,
      author,
      author_avatar,
      category_id,
      featured,
      views,
      likes,
      read_time,
      status,
      published_date
    })

    const experience = await ExperienceModel.getById(experienceId)

    return NextResponse.json({
      success: true,
      data: experience
    }, { status: 201 })
  } catch (error) {
    console.error('Experiences API Error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create experience'
      },
      { status: 500 }
    )
  }
}
