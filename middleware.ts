import { withAuth } from 'next-auth/middleware'

export default withAuth(
  function middleware(req) {
    // Add any additional middleware logic here
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Temporarily allow all admin routes for debugging
        console.log('🔒 Middleware check:', {
          path: req.nextUrl.pathname,
          hasToken: !!token,
          tokenRole: token?.role
        })

        // Allow access to login page
        if (req.nextUrl.pathname === '/admin/login') {
          return true
        }

        // Temporarily allow all admin routes for debugging
        if (req.nextUrl.pathname.startsWith('/admin')) {
          console.log('🔒 Allowing admin route for debugging:', req.nextUrl.pathname)
          return true // Temporarily allow all admin routes
        }

        return true
      }
    }
  }
)

export const config = {
  matcher: ['/admin/((?!login).*)']
}
