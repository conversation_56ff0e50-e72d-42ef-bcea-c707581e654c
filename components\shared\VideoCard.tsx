import Image from 'next/image'
import Link from 'next/link'
import { Badge } from '@/components/ui/badge'
import { Play, Clock, Eye } from 'lucide-react'

interface Video {
  id: number
  title: string
  slug: string
  description?: string
  video_url?: string
  thumbnail?: string
  duration?: string
  views?: number
  category_name?: string
  featured?: boolean
}

interface VideoCardProps {
  video: Video
  showCategory?: boolean
  showViews?: boolean
  showDuration?: boolean
  className?: string
}

export default function VideoCard({ 
  video, 
  showCategory = true,
  showViews = true,
  showDuration = true,
  className = ""
}: VideoCardProps) {
  const defaultThumbnail = "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600"

  return (
    <div className={`bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 ${className}`}>
      <div className="relative h-48 group">
        <Image
          src={video.thumbnail || defaultThumbnail}
          alt={video.title}
          fill
          className="object-cover"
        />
        
        {/* Play button overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="bg-white bg-opacity-90 rounded-full p-4">
            <Play className="h-8 w-8 text-gray-900 fill-current" />
          </div>
        </div>
        
        {video.featured && (
          <div className="absolute top-4 left-4">
            <Badge className="bg-red-500 text-white">
              Nổi bật
            </Badge>
          </div>
        )}
        
        {showCategory && video.category_name && (
          <div className="absolute top-4 right-4">
            <Badge className="bg-blue-500 text-white">
              {video.category_name}
            </Badge>
          </div>
        )}
        
        {showDuration && video.duration && (
          <div className="absolute bottom-4 right-4">
            <Badge className="bg-black bg-opacity-70 text-white">
              <Clock className="h-3 w-3 mr-1" />
              {video.duration}
            </Badge>
          </div>
        )}
      </div>
      
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
          <Link 
            href={`/video-cong-trinh-hoan-thien/${video.slug}`}
            className="hover:text-blue-600 transition-colors"
          >
            {video.title}
          </Link>
        </h3>
        
        {video.description && (
          <p className="text-gray-600 mb-4 line-clamp-3">
            {video.description}
          </p>
        )}
        
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            {showViews && video.views !== undefined && (
              <div className="flex items-center">
                <Eye className="h-4 w-4 mr-1" />
                {video.views.toLocaleString()} lượt xem
              </div>
            )}
          </div>
          
          <Link 
            href={`/video-cong-trinh-hoan-thien/${video.slug}`}
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            Xem video →
          </Link>
        </div>
      </div>
    </div>
  )
}
