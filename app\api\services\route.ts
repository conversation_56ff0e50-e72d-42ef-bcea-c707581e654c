import { NextRequest, NextResponse } from 'next/server'
import { ServiceModel } from '@/lib/models/service'

// GET /api/services
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category_id = searchParams.get('category_id')
    const featured = searchParams.get('featured')
    const status = searchParams.get('status')
    const limit = searchParams.get('limit')
    const offset = searchParams.get('offset')
    const search = searchParams.get('search')

    // Handle search
    if (search) {
      const services = await ServiceModel.search(search, limit ? parseInt(limit) : 10)
      return NextResponse.json({
        success: true,
        data: services
      })
    }

    // Handle regular filtering
    const filters: any = {}
    
    if (category_id) filters.category_id = parseInt(category_id)
    if (featured !== null) filters.featured = featured === 'true'
    if (status) filters.status = status
    if (limit) filters.limit = parseInt(limit)
    if (offset) filters.offset = parseInt(offset)

    const services = await ServiceModel.getAll(filters)

    // Get total count for pagination
    const totalCount = await ServiceModel.getCount(filters)
    const totalPages = Math.ceil(totalCount / (filters.limit || 10))

    return NextResponse.json({
      success: true,
      data: services,
      pagination: {
        page: Math.floor((filters.offset || 0) / (filters.limit || 10)) + 1,
        limit: filters.limit || 10,
        total: totalCount,
        totalPages
      }
    })
  } catch (error) {
    console.error('Services API Error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch services'
      },
      { status: 500 }
    )
  }
}

// POST /api/services
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      title,
      slug,
      description,
      content,
      image,
      features,
      price_range,
      duration,
      category_id,
      featured,
      status
    } = body

    if (!title) {
      return NextResponse.json(
        {
          success: false,
          error: 'Title is required'
        },
        { status: 400 }
      )
    }

    const serviceId = await ServiceModel.create({
      title,
      slug,
      description,
      content,
      image,
      features,
      price_range,
      duration,
      category_id,
      featured,
      status
    })

    const service = await ServiceModel.getById(serviceId)

    return NextResponse.json({
      success: true,
      data: service
    }, { status: 201 })
  } catch (error) {
    console.error('Services API Error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create service'
      },
      { status: 500 }
    )
  }
}
