const stats = [
  {
    id: 1,
    number: "+2,500",
    title: "DỰ ÁN HOÀN THÀNH",
    description: "Hơn 2,500 dự án nội thất đã được hoàn thiện với chất lượng cao nhất",
  },
  {
    id: 2,
    number: "95%",
    title: "KHÁCH HÀNG HÀI LÒNG",
    description: "95% khách hàng đánh giá cao chất lượng dịch vụ của chúng tôi",
  },
  {
    id: 3,
    number: "+400",
    title: "ĐỐI TÁC TIN TƯỞNG",
    description: "Hơn 400 đối tác và nhà cung cấp uy tín trên toàn quốc",
  },
  {
    id: 4,
    number: "15+",
    title: "NĂM KINH NGHIỆM",
    description: "Hơn 15 năm kinh nghiệm trong lĩnh vực thiết kế nội thất",
  },
]

export default function Statistics() {
  return (
    <section className="py-20 bg-[#1F2937] text-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 tracking-wider">NHỮNG BƯỚC TIẾN CỦA CHÚNG TÔI</h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Những con số ấn tượng khẳng định uy tín và chất lượng dịch vụ
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat) => (
            <div
              key={stat.id}
              className="text-center group hover:transform hover:scale-105 transition-all duration-300"
            >
              <div className="mb-4">
                <span className="text-5xl md:text-6xl font-bold text-[#3B82F6] group-hover:text-white transition-colors duration-300">
                  {stat.number}
                </span>
              </div>
              <h3 className="text-xl font-bold mb-4 tracking-wide">{stat.title}</h3>
              <p className="text-gray-300 leading-relaxed">{stat.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
