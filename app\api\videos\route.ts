import { NextRequest, NextResponse } from 'next/server'
import { VideoModel } from '@/lib/models/video'

// GET /api/videos
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category_id = searchParams.get('category_id')
    const featured = searchParams.get('featured')
    const status = searchParams.get('status')
    const limit = searchParams.get('limit')
    const offset = searchParams.get('offset')
    const search = searchParams.get('search')
    const popular = searchParams.get('popular')

    // Handle search
    if (search) {
      const videos = await VideoModel.search(search, limit ? parseInt(limit) : 10)
      return NextResponse.json({
        success: true,
        data: videos
      })
    }

    // Handle popular videos
    if (popular === 'true') {
      const videos = await VideoModel.getPopular(limit ? parseInt(limit) : 10)
      return NextResponse.json({
        success: true,
        data: videos
      })
    }

    // Handle regular filtering
    const filters: any = {}
    
    if (category_id) filters.category_id = parseInt(category_id)
    if (featured !== null) filters.featured = featured === 'true'
    if (status) filters.status = status
    if (limit) filters.limit = parseInt(limit)
    if (offset) filters.offset = parseInt(offset)

    const videos = await VideoModel.getAll(filters)

    return NextResponse.json({
      success: true,
      data: videos
    })
  } catch (error) {
    console.error('Videos API Error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch videos'
      },
      { status: 500 }
    )
  }
}

// POST /api/videos
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      title,
      slug,
      description,
      video_url,
      thumbnail,
      duration,
      views,
      category_id,
      featured,
      status
    } = body

    if (!title) {
      return NextResponse.json(
        {
          success: false,
          error: 'Title is required'
        },
        { status: 400 }
      )
    }

    const videoId = await VideoModel.create({
      title,
      slug,
      description,
      video_url,
      thumbnail,
      duration,
      views,
      category_id,
      featured,
      status
    })

    const video = await VideoModel.getById(videoId)

    return NextResponse.json({
      success: true,
      data: video
    }, { status: 201 })
  } catch (error) {
    console.error('Videos API Error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create video'
      },
      { status: 500 }
    )
  }
}
