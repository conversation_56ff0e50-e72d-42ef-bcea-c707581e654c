# Database Setup Guide - MariaDB Migration

## 🎯 Tổng Quan

Dự án đã được chuyển đổi từ MongoDB sang MariaDB để tối ưu hiệu suất và dễ dàng quản lý. Tất cả các models, API routes và database schema đã được tạo sẵn.

## 📋 Yêu Cầu Hệ Thống

- Node.js 18+
- MariaDB 10.6+ hoặc MySQL 8.0+
- npm hoặc yarn

## 🚀 Cài Đặt Database

### 1. Cài đặt MariaDB

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install mariadb-server
sudo mysql_secure_installation
```

**macOS (với Homebrew):**
```bash
brew install mariadb
brew services start mariadb
```

**Windows:**
Tải và cài đặt từ: https://mariadb.org/download/

### 2. Tạo Database

```sql
-- Đăng nhập vào MariaDB
mysql -u root -p

-- Tạo database
CREATE DATABASE interior_web CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Tạo user (tù<PERSON> chọ<PERSON>)
CREATE USER 'interior_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON interior_web.* TO 'interior_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. Cấu Hình Environment

```bash
# Copy file example
cp .env.local.example .env.local

# Chỉnh sửa thông tin database
nano .env.local
```

Cập nhật thông tin trong `.env.local`:
```env
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=interior_web
```

### 4. Khởi Tạo Database

```bash
# Cài đặt dependencies
npm install

# Khởi tạo database và seed data
npm run db:init
```

## 📊 Database Schema

### Tables Created:

1. **categories** - Danh mục cho tất cả content types
2. **services** - Dịch vụ thiết kế nội thất
3. **projects** - Dự án đã hoàn thành
4. **articles** - Bài viết cẩm nang
5. **videos** - Video công trình
6. **experiences** - Chia sẻ kinh nghiệm

### Key Features:
- ✅ Foreign key relationships
- ✅ JSON fields cho arrays (features, images)
- ✅ Full-text search support
- ✅ Automatic timestamps
- ✅ Status management (draft/published/archived)
- ✅ SEO-friendly slugs

## 🔌 API Endpoints

### Categories
- `GET /api/categories` - Lấy tất cả categories
- `GET /api/categories?type=services` - Lấy categories với count
- `POST /api/categories` - Tạo category mới

### Services
- `GET /api/services` - Lấy tất cả services
- `GET /api/services?featured=true` - Lấy services nổi bật
- `GET /api/services?search=keyword` - Tìm kiếm services
- `POST /api/services` - Tạo service mới

### Projects
- `GET /api/projects` - Lấy tất cả projects
- `GET /api/projects?category_id=1` - Lọc theo category
- `GET /api/projects?year=2024` - Lọc theo năm
- `POST /api/projects` - Tạo project mới

### Articles
- `GET /api/articles` - Lấy tất cả articles
- `GET /api/articles?popular=true` - Lấy articles phổ biến
- `POST /api/articles` - Tạo article mới

### Videos & Experiences
- Tương tự như các endpoints trên

## 🎨 UI Updates

### Introduction Component
- ✅ Design mới với gradient background
- ✅ Interactive video player
- ✅ Achievement statistics
- ✅ Enhanced animations
- ✅ Modern card design
- ✅ Responsive layout

## 🔧 Development Commands

```bash
# Chạy development server
npm run dev

# Build production
npm run build

# Khởi tạo lại database
npm run db:init

# Lint code
npm run lint
```

## 📝 Next Steps

1. **Setup Admin Panel** - Tạo giao diện quản trị
2. **Authentication** - Implement NextAuth.js
3. **File Upload** - API cho upload hình ảnh
4. **Connect Frontend** - Kết nối các trang với API
5. **Testing** - Viết tests cho API endpoints

## 🐛 Troubleshooting

### Database Connection Issues
```bash
# Kiểm tra MariaDB service
sudo systemctl status mariadb

# Restart service
sudo systemctl restart mariadb

# Check logs
sudo tail -f /var/log/mysql/error.log
```

### Permission Issues
```sql
-- Grant permissions
GRANT ALL PRIVILEGES ON interior_web.* TO 'your_user'@'localhost';
FLUSH PRIVILEGES;
```

## 📞 Support

Nếu gặp vấn đề, hãy kiểm tra:
1. Database connection string trong `.env.local`
2. MariaDB service đang chạy
3. User có đủ permissions
4. Port 3306 không bị block

---

**Hoàn thành:** ✅ Database Migration từ MongoDB sang MariaDB
**Tiếp theo:** 🔄 Connect Frontend với Database APIs
